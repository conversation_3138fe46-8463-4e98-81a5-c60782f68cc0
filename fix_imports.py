#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SeedVisionTrain 导入路径修复脚本

将所有硬编码的外部路径修改为相对路径，使项目完全独立
"""

import os
import re
import glob

def fix_file_imports(file_path):
    """修复单个文件的导入路径"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # 替换硬编码的路径
        patterns_to_fix = [
            # 硬编码的项目路径
            (r"sys\.path\.append\('E:\\Proj\\pytorch-model-train'\)", 
             "# 添加项目根路径\nproject_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))\nsys.path.append(project_root)"),
            
            # 其他可能的硬编码路径
            (r"sys\.path\.append\('E:/Proj/pytorch-model-train'\)", 
             "# 添加项目根路径\nproject_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))\nsys.path.append(project_root)"),
             
            # 修复相对路径计算
            (r"sys\.path\.append\(os\.path\.dirname\(os\.path\.dirname\(os\.path\.dirname\(os\.path\.dirname\(os\.path\.abspath\(__file__\)\)\)\)\)\)",
             "# 添加项目根路径\nproject_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))\nsys.path.append(project_root)"),
        ]
        
        for pattern, replacement in patterns_to_fix:
            content = re.sub(pattern, replacement, content)
        
        # 如果内容有变化，写回文件
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ 修复: {file_path}")
            return True
        else:
            print(f"⏭️  跳过: {file_path} (无需修改)")
            return False
            
    except Exception as e:
        print(f"❌ 错误: {file_path} - {e}")
        return False

def find_python_files():
    """查找所有Python文件"""
    python_files = []
    
    # 查找所有.py文件
    for root, dirs, files in os.walk('.'):
        # 跳过__pycache__目录
        dirs[:] = [d for d in dirs if d != '__pycache__']
        
        for file in files:
            if file.endswith('.py'):
                file_path = os.path.join(root, file)
                python_files.append(file_path)
    
    return python_files

def main():
    """主函数"""
    print("🔧 SeedVisionTrain 导入路径修复工具")
    print("=" * 60)
    
    # 切换到项目根目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    
    # 查找所有Python文件
    python_files = find_python_files()
    print(f"📁 找到 {len(python_files)} 个Python文件")
    
    # 修复每个文件
    fixed_count = 0
    for file_path in python_files:
        if fix_file_imports(file_path):
            fixed_count += 1
    
    print("\n" + "=" * 60)
    print(f"🎉 修复完成！共修复 {fixed_count} 个文件")
    
    # 显示修复建议
    print("\n📋 手动检查建议:")
    print("1. 检查所有 'from utils.xxx import xxx' 的导入")
    print("2. 确保项目根目录路径计算正确")
    print("3. 运行测试验证修复效果")
    
    print("\n🧪 测试命令:")
    print("python main.py")
    print("python test_config_mongo_tool.py")

if __name__ == "__main__":
    main()
