#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SeedVision v1 - 脚本管理器

提供统一的脚本管理界面，方便执行各种系统脚本。

使用方法：
python scripts_manager.py
"""

import os
import sys
import subprocess
from datetime import datetime

# 添加项目根路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.append(project_root)

class Colors:
    RED = '\033[91m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    MAGENTA = '\033[95m'
    CYAN = '\033[96m'
    WHITE = '\033[97m'
    BOLD = '\033[1m'
    END = '\033[0m'

def show_main_menu():
    """显示主菜单"""
    print(f"\n{Colors.BOLD}📜 SeedVision v1 - 脚本管理器{Colors.END}")
    print("=" * 60)
    print(f"{Colors.CYAN}🚀 部署脚本{Colors.END}")
    print("1. 部署环境检查")
    print("2. 项目独立性验证")
    print()
    print(f"{Colors.YELLOW}🔧 维护脚本{Colors.END}")
    print("3. 导入路径修复")
    print("4. 配置工具测试")
    print()
    print(f"{Colors.GREEN}🛠️ 工具脚本{Colors.END}")
    print("5. 项目整理 (清理缓存)")
    print("6. 日志整理")
    print("7. 生成项目报告")
    print("8. 完整项目整理")
    print()
    print(f"{Colors.MAGENTA}🧪 测试脚本{Colors.END}")
    print("9. 快速测试")
    print("10. 完整测试")
    print()
    print("0. 退出")
    print("=" * 60)

def run_script(script_path, args="", description=""):
    """运行脚本"""
    print(f"\n{Colors.BLUE}[RUNNING]{Colors.END} {description}")
    print("=" * 60)
    print(f"执行: python {script_path} {args}")
    print(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("-" * 60)
    
    try:
        # 构建完整命令
        cmd = [sys.executable, script_path] + (args.split() if args else [])
        
        # 执行脚本
        result = subprocess.run(
            cmd,
            cwd=project_root,
            capture_output=False,  # 直接显示输出
            text=True
        )
        
        print("-" * 60)
        if result.returncode == 0:
            print(f"{Colors.GREEN}[SUCCESS]{Colors.END} 脚本执行成功")
        else:
            print(f"{Colors.RED}[FAILED]{Colors.END} 脚本执行失败 (返回码: {result.returncode})")
        
        return result.returncode == 0
        
    except Exception as e:
        print(f"{Colors.RED}[ERROR]{Colors.END} 执行异常: {e}")
        return False

def deployment_check():
    """部署环境检查"""
    return run_script(
        "scripts/deployment/deployment_check.py",
        "",
        "部署环境检查 - 检查Python版本、依赖包、项目结构"
    )

def verify_independence():
    """项目独立性验证"""
    return run_script(
        "scripts/deployment/verify_independence.py",
        "",
        "项目独立性验证 - 确保项目完全独立，无外部依赖"
    )

def fix_imports():
    """导入路径修复"""
    return run_script(
        "scripts/maintenance/fix_imports.py",
        "",
        "导入路径修复 - 自动修复硬编码路径，转换为相对路径"
    )

def test_config_tool():
    """配置工具测试"""
    return run_script(
        "scripts/maintenance/test_config_mongo_tool.py",
        "",
        "配置工具测试 - 测试MongoDB配置管理工具功能"
    )

def organize_clean():
    """项目整理 - 清理"""
    return run_script(
        "scripts/utilities/organize_project.py",
        "--clean",
        "项目清理 - 清理缓存文件和临时文件"
    )

def organize_logs():
    """项目整理 - 日志"""
    return run_script(
        "scripts/utilities/organize_project.py",
        "--logs",
        "日志整理 - 按日期归档日志文件"
    )

def organize_report():
    """项目整理 - 报告"""
    return run_script(
        "scripts/utilities/organize_project.py",
        "--report",
        "项目报告 - 生成详细的项目统计报告"
    )

def organize_all():
    """项目整理 - 全部"""
    return run_script(
        "scripts/utilities/organize_project.py",
        "--all",
        "完整项目整理 - 清理、整理、报告"
    )

def quick_test():
    """快速测试"""
    return run_script(
        "quick_test.py",
        "quick",
        "快速测试 - 基础功能验证 (2-3分钟)"
    )

def full_test():
    """完整测试"""
    return run_script(
        "tests/runners/run_tests.py",
        "full",
        "完整测试 - 所有功能验证 (15-30分钟)"
    )

def main():
    """主函数"""
    # 切换到项目根目录
    os.chdir(project_root)
    
    # 脚本功能映射
    script_functions = {
        '1': deployment_check,
        '2': verify_independence,
        '3': fix_imports,
        '4': test_config_tool,
        '5': organize_clean,
        '6': organize_logs,
        '7': organize_report,
        '8': organize_all,
        '9': quick_test,
        '10': full_test
    }
    
    while True:
        show_main_menu()
        choice = input(f"\n{Colors.BOLD}请选择功能 (0-10): {Colors.END}").strip()
        
        if choice == '0':
            print(f"\n{Colors.GREEN}👋 再见！{Colors.END}")
            break
        elif choice in script_functions:
            try:
                success = script_functions[choice]()
                if success:
                    input(f"\n{Colors.GREEN}按回车键继续...{Colors.END}")
                else:
                    input(f"\n{Colors.YELLOW}按回车键继续...{Colors.END}")
            except KeyboardInterrupt:
                print(f"\n\n{Colors.YELLOW}[INTERRUPTED]{Colors.END} 操作被用户中断")
                input(f"{Colors.YELLOW}按回车键继续...{Colors.END}")
            except Exception as e:
                print(f"\n{Colors.RED}[ERROR]{Colors.END} 执行异常: {e}")
                input(f"{Colors.RED}按回车键继续...{Colors.END}")
        else:
            print(f"\n{Colors.RED}❌ 无效选择，请重新输入{Colors.END}")
            input(f"{Colors.YELLOW}按回车键继续...{Colors.END}")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print(f"\n\n{Colors.YELLOW}程序被用户中断{Colors.END}")
    except Exception as e:
        print(f"\n{Colors.RED}程序异常: {e}{Colors.END}")
        sys.exit(1)
