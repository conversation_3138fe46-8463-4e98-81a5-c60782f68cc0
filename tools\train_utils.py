"""
训练相关的工具函数
"""

import torch
import time
import numpy as np
import os
# 添加项目根路径
import sys
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(project_root)
from utils.logger import logger
from PIL.Image import Image
import ultralytics
from torchvision import transforms

import json
import os
import torch
from typing import Dict, Any, Optional, Union, Tuple

def get_model_init_params(
    model_name: str,
    config_path: str = 'config.json',
    default_params: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    获取模型初始化参数，优先从配置文件读取，如果没有则使用默认参数

    Args:
        model_name: 模型名称，用于在配置文件中查找对应的参数
        config_path: 配置文件路径，默认为'config.json'
        default_params: 默认参数字典，当配置文件中没有对应参数时使用

    Returns:
        Dict[str, Any]: 模型初始化参数字典

    Raises:
        FileNotFoundError: 当配置文件不存在且未提供默认参数时
        ValueError: 当配置文件格式错误时
    """
    # 设置默认参数（如果未提供）
    if default_params is None:
        default_params = {}

    # 尝试从配置文件加载参数
    params = default_params.copy()

    try:
        # 检查配置文件是否存在
        if os.path.exists(config_path) and os.path.getsize(config_path) > 0:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)

            # 获取模型特定配置
            model_config = config.get('models', {}).get(model_name, {})

            # 更新参数
            if model_config:
                params.update(model_config)
                print(f"已从配置文件 '{config_path}' 加载 '{model_name}' 模型参数")
            else:
                print(f"配置文件中未找到 '{model_name}' 模型参数，使用默认参数")
        else:
            print(f"配置文件 '{config_path}' 不存在或为空，使用默认参数")

    except json.JSONDecodeError as e:
        raise ValueError(f"配置文件 '{config_path}' 格式错误: {e}")

    except Exception as e:
        print(f"加载配置文件时出错: {e}，使用默认参数")

    return params

def get_device(device_preference: Optional[Union[str, int]] = None) -> torch.device:
    """
    获取可用的计算设备

    Args:
        device_preference: 首选设备，可以是'cuda'、'cpu'或GPU索引（整数）

    Returns:
        torch.device: 可用的计算设备
    """
    # 如果指定了设备偏好
    if device_preference is not None:
        # 如果偏好是整数，视为GPU索引
        if isinstance(device_preference, int):
            if torch.cuda.is_available() and device_preference < torch.cuda.device_count():
                return torch.device(f'cuda:{device_preference}')
            else:
                print(f"GPU {device_preference} 不可用，回退到CPU")
                return torch.device('cpu')

        # 如果偏好是字符串
        elif device_preference.lower() == 'cuda' and torch.cuda.is_available():
            return torch.device('cuda:0')
        elif device_preference.lower() == 'cpu':
            return torch.device('cpu')

    # 默认行为：如果有CUDA则使用，否则使用CPU
    if torch.cuda.is_available():
        return torch.device('cuda:0')
    else:
        return torch.device('cpu')

def get_optimizer(
    model: torch.nn.Module,
    optimizer_name: str = 'adam',
    learning_rate: float = 0.001,
    weight_decay: float = 0.0,
    **kwargs
) -> torch.optim.Optimizer:
    """
    根据名称获取优化器实例

    Args:
        model: 要优化的模型
        optimizer_name: 优化器名称，支持'adam'、'sgd'、'adamw'等
        learning_rate: 学习率
        weight_decay: 权重衰减
        **kwargs: 传递给优化器的其他参数

    Returns:
        torch.optim.Optimizer: 优化器实例

    Raises:
        ValueError: 当指定的优化器名称不受支持时
    """
    optimizer_name = optimizer_name.lower()

    if optimizer_name == 'adam':
        return torch.optim.Adam(
            model.parameters(),
            lr=learning_rate,
            weight_decay=weight_decay,
            **kwargs
        )
    elif optimizer_name == 'sgd':
        return torch.optim.SGD(
            model.parameters(),
            lr=learning_rate,
            weight_decay=weight_decay,
            momentum=kwargs.get('momentum', 0.9),
            **kwargs
        )
    elif optimizer_name == 'adamw':
        return torch.optim.AdamW(
            model.parameters(),
            lr=learning_rate,
            weight_decay=weight_decay,
            **kwargs
        )
    else:
        raise ValueError(f"不支持的优化器: {optimizer_name}")

def train_epoch(model, data_loader, criterion, optimizer, device):
    """
    训练一个epoch

    Args:
        model: 模型
        data_loader: 数据加载器
        criterion: 损失函数
        optimizer: 优化器
        device: 设备

    Returns:
        float: 平均训练损失
    """
    model.train()
    running_loss = 0.0
    start_time = time.time()

    for i, (inputs, targets) in enumerate(data_loader):
        # 将数据移至设备
        inputs, targets = inputs.to(device), targets.to(device)

        # 前向传播
        optimizer.zero_grad()
        outputs = model(inputs)

        # 计算损失
        loss = criterion(outputs, targets)

        # 反向传播和优化
        loss.backward()
        optimizer.step()

        # 累计损失
        running_loss += loss.item()

        # 打印进度
        if (i + 1) % 10 == 0:
            logger.info(f"Batch {i+1}/{len(data_loader)}, Loss: {loss.item():.4f}")

    # 计算平均损失
    avg_loss = running_loss / len(data_loader)

    # 打印训练时间
    elapsed_time = time.time() - start_time
    logger.info(f"训练耗时: {elapsed_time:.2f}秒")

    return avg_loss

def validate(model, data_loader, criterion, device):
    """
    验证模型

    Args:
        model: 模型
        data_loader: 数据加载器
        criterion: 损失函数
        device: 设备

    Returns:
        float: 平均验证损失
    """
    model.eval()
    running_loss = 0.0

    with torch.no_grad():
        for inputs, targets in data_loader:
            # 将数据移至设备
            inputs, targets = inputs.to(device), targets.to(device)

            # 前向传播
            outputs = model(inputs)

            # 计算损失
            loss = criterion(outputs, targets)

            # 累计损失
            running_loss += loss.item()

    # 计算平均损失
    avg_loss = running_loss / len(data_loader)
    logger.info(f"验证损失: {avg_loss:.4f}")

    return avg_loss

def save_checkpoint(model, optimizer, epoch, loss, save_path):
    """
    保存检查点

    Args:
        model: 模型
        optimizer: 优化器
        epoch: 当前轮次
        loss: 当前损失
        save_path: 保存路径

    Returns:
        None
    """
    # 确保目录存在
    os.makedirs(os.path.dirname(save_path), exist_ok=True)

    # 创建检查点字典
    checkpoint = {
        'epoch': epoch,
        'model_state_dict': model.state_dict(),
        'optimizer_state_dict': optimizer.state_dict(),
        'loss': loss
    }

    # 保存检查点
    torch.save(checkpoint, save_path)
    logger.info(f"检查点已保存至: {save_path}")

def load_checkpoint(model, optimizer, checkpoint_path, device):
    """
    加载检查点

    Args:
        model: 模型
        optimizer: 优化器
        checkpoint_path: 检查点路径
        device: 设备

    Returns:
        int: 加载的轮次
        float: 加载的损失
    """
    if not os.path.exists(checkpoint_path):
        logger.error(f"检查点文件不存在: {checkpoint_path}")
        return 0, 0.0

    # 加载检查点
    checkpoint = torch.load(checkpoint_path, map_location=device)

    # 恢复模型和优化器状态
    model.load_state_dict(checkpoint['model_state_dict'])
    optimizer.load_state_dict(checkpoint['optimizer_state_dict'])

    logger.info(f"成功加载检查点: {checkpoint_path}")
    return checkpoint['epoch'], checkpoint['loss']

def calculate_metrics(outputs, targets):
    """
    计算评估指标

    Args:
        outputs: 模型输出
        targets: 目标值

    Returns:
        dict: 包含各种评估指标的字典
    """
    # 将张量转换为numpy数组
    if isinstance(outputs, torch.Tensor):
        outputs = outputs.detach().cpu().numpy()
    if isinstance(targets, torch.Tensor):
        targets = targets.detach().cpu().numpy()

    # 计算均方误差
    mse = np.mean((outputs - targets) ** 2)

    # 计算平均绝对误差
    mae = np.mean(np.abs(outputs - targets))

    # 计算R²分数
    ss_total = np.sum((targets - np.mean(targets)) ** 2)
    ss_residual = np.sum((targets - outputs) ** 2)
    r2 = 1 - (ss_residual / ss_total) if ss_total != 0 else 0

    return {
        'mse': mse,
        'mae': mae,
        'r2': r2
    }

def setup_optimizer(model, config):
    """
    设置优化器

    Args:
        model: 模型
        config: 配置参数

    Returns:
        optimizer: 优化器
    """
    optimizer_name = config.get('optimizer', 'adam').lower()
    lr = config.get('learning_rate', 0.001)
    weight_decay = config.get('weight_decay', 0.0)

    if optimizer_name == 'adam':
        optimizer = torch.optim.Adam(model.parameters(), lr=lr, weight_decay=weight_decay)
    elif optimizer_name == 'sgd':
        momentum = config.get('momentum', 0.9)
        optimizer = torch.optim.SGD(model.parameters(), lr=lr, momentum=momentum, weight_decay=weight_decay)
    elif optimizer_name == 'adamw':
        optimizer = torch.optim.AdamW(model.parameters(), lr=lr, weight_decay=weight_decay)
    else:
        logger.warning(f"未知的优化器: {optimizer_name}，使用默认的Adam")
        optimizer = torch.optim.Adam(model.parameters(), lr=lr, weight_decay=weight_decay)

    return optimizer

def setup_scheduler(optimizer, config):
    """
    设置学习率调度器

    Args:
        optimizer: 优化器
        config: 配置参数

    Returns:
        scheduler: 学习率调度器
    """
    scheduler_name = config.get('scheduler', 'none').lower()

    if scheduler_name == 'step':
        step_size = config.get('step_size', 30)
        gamma = config.get('gamma', 0.1)
        scheduler = torch.optim.lr_scheduler.StepLR(optimizer, step_size=step_size, gamma=gamma)
    elif scheduler_name == 'cosine':
        t_max = config.get('t_max', 100)
        eta_min = config.get('eta_min', 0)
        scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=t_max, eta_min=eta_min)
    elif scheduler_name == 'plateau':
        patience = config.get('patience', 10)
        factor = config.get('factor', 0.1)
        scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='min', factor=factor, patience=patience)
    elif scheduler_name == 'none':
        scheduler = None
    else:
        logger.warning(f"未知的调度器: {scheduler_name}，不使用调度器")
        scheduler = None

    return scheduler

def train_model(model, train_loader, val_loader, criterion, optimizer, scheduler, config, device):
    """
    训练模型的完整流程

    Args:
        model: 模型
        train_loader: 训练数据加载器
        val_loader: 验证数据加载器
        criterion: 损失函数
        optimizer: 优化器
        scheduler: 学习率调度器， 用来调整学习率
        config: 配置参数
        device: 设备

    Returns:
        model: 训练好的模型
        dict: 训练历史记录
    """
    # 获取配置参数
    epochs = config.get('epochs', 100)
    save_dir = config.get('save_dir', 'weights')
    model_name = config.get('model_name', 'model.pt')
    checkpoint_interval = config.get('checkpoint_interval', 10)

    # 确保保存目录存在
    os.makedirs(save_dir, exist_ok=True)

    # 训练历史记录
    history = {
        'train_loss': [],
        'val_loss': [],
        'metrics': []
    }

    # 最佳验证损失
    best_val_loss = float('inf')

    # 训练循环
    for epoch in range(epochs):
        # 训练一个epoch
        train_loss = train_epoch(model, train_loader, criterion, optimizer, device)
        history['train_loss'].append(train_loss)

        # 验证
        val_loss = validate(model, val_loader, criterion, device)
        history['val_loss'].append(val_loss)

        # 更新学习率
        if scheduler is not None:
            if isinstance(scheduler, torch.optim.lr_scheduler.ReduceLROnPlateau):
                scheduler.step(val_loss)
            else:
                scheduler.step()

        # 打印当前学习率
        current_lr = optimizer.param_groups[0]['lr']
        logger.info(f"当前学习率: {current_lr:.6f}")

        # 保存检查点
        if (epoch + 1) % checkpoint_interval == 0:
            checkpoint_path = os.path.join(save_dir, f"checkpoint_epoch_{epoch+1}.pt")
            save_checkpoint(model, optimizer, epoch, val_loss, checkpoint_path)

        # 保存最佳模型
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            best_model_path = os.path.join(save_dir, f"best_{model_name}")
            torch.save(model.state_dict(), best_model_path)
            logger.info(f"保存最佳模型，验证损失: {best_val_loss:.4f}")

    # 保存最终模型
    final_model_path = os.path.join(save_dir, model_name)
    torch.save(model.state_dict(), final_model_path)
    logger.info(f"训练完成，最终模型已保存至: {final_model_path}")

    return model, history



def process_yoloOutput(yolo_output: ultralytics.engine.results.Results, image: Image, device = 'cuda', transform = None):
    """
    处理YOLO的输出，返回裁剪后的目标区域和对应的检测信息

    Args:
        yolo_output: YOLO的输出结果

    Returns:
        List[Tuple[Tensor, dict]]: 包含裁剪图像和检测信息的列表
            - Tensor: 裁剪后的图像 (C, H, W)
            - dict: 包含检测框坐标和置信度等信息
    """

    cropped_tensors = []

    # 遍历YOLO检测结果
    for detection in yolo_output[0].boxes:
        # 获取置信度
        confidence = detection.conf.item()
        if confidence < 0.5: # 置信度阈值
            logger.warning(f"检测框坐标：{x1, y1, x2, y2}，置信度：{confidence} 低于阈值 ，判断为非油菜籽，舍弃该框")
            continue

        # 获取检测框坐标
        x1, y1, x2, y2 = detection.xyxy[0].cpu().numpy()

        # 获取类别ID
        class_id = detection.cls.item()

        # 裁剪目标区域
        cropped = image.crop((x1, y1, x2, y2))
        if transform is not None:
            cropped_tensor = transform(cropped).unsqueeze(0)

            cropped_tensor = cropped_tensor.to(device)
        else:
            # 原图直接转换成tensor
            cropped_tensor = torch.from_numpy(np.array(cropped)).permute(2, 0, 1).float() / 255.0

        # # 转换为Tensor并调整通道顺序 (H, W, C) -> (C, H, W)
        # cropped_tensor = torch.from_numpy(cropped).permute(2, 0, 1).float() / 255.0

        # # 保存检测信息
        # detection_info = {
        #     'box': [x1, y1, x2, y2],
        #     'confidence': confidence,
        #     # 'class_id': class_id # 无用，只有一个标签
        # }

        # processed_results.append((cropped_tensor, detection_info))
        cropped_tensors.append(cropped_tensor)

    return cropped_tensors