#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SeedVision v1 训练系统主入口

功能：
1. 单个配置训练
2. 批量训练队列管理
3. GPU显存管理
4. 智能任务调度

使用方法：
python runners/training/main.py
"""

import os
import sys
import torch
import torch.multiprocessing as mp
import time
from datetime import datetime
import GPUtil
import argparse

# 添加项目根路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(project_root)

# 导入自定义模块
from tools.training.train import train_model
from config.config_loader import ConfigLoader

# 设置多进程启动方法
mp.set_start_method('spawn', force=True)

# 颜色定义
class Colors:
    RED = '\033[91m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    MAGENTA = '\033[95m'
    CYAN = '\033[96m'
    WHITE = '\033[97m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'
    END = '\033[0m'

# 全局变量
MAX_GPU_MEMORY = 8  # 最大GPU显存限制(GB)
LOCK = mp.Lock()  # 进程锁，用于同步显存使用

def get_gpu_memory_usage():
    """获取当前GPU显存使用情况(GB)"""
    try:
        gpus = GPUtil.getGPUs()
        if gpus:
            return gpus[0].memoryUsed / 1024  # 转换为GB
        return 0
    except:
        return 0

def get_detailed_gpu_info():
    """获取详细的GPU信息"""
    try:
        gpus = GPUtil.getGPUs()
        if gpus:
            gpu = gpus[0]
            used_mb = gpu.memoryUsed
            total_mb = gpu.memoryTotal
            utilization = gpu.load * 100
            return {
                'used_mb': used_mb,
                'total_mb': total_mb,
                'used_gb': used_mb / 1024,
                'total_gb': total_mb / 1024,
                'utilization': utilization,
                'free_mb': total_mb - used_mb,
                'free_gb': (total_mb - used_mb) / 1024
            }
        return None
    except:
        return None



def wait_for_gpu_memory(required_memory=1.0, check_interval=5, max_wait_time=300):
    """
    等待直到有足够的GPU显存可用

    参数:
        required_memory: 需要的显存量(GB)
        check_interval: 检查间隔(秒)
        max_wait_time: 最大等待时间(秒)
    """
    start_time = time.time()

    while time.time() - start_time < max_wait_time:
        with LOCK:
            current_usage = get_gpu_memory_usage()
            available_memory = MAX_GPU_MEMORY - current_usage

            if available_memory >= required_memory:
                return True

        print(f"Waiting for GPU memory... Current: {current_usage:.2f}GB, Required: {required_memory:.2f}GB")
        time.sleep(check_interval)

    print(f"Warning: Timeout waiting for GPU memory after {max_wait_time}s")
    return False

def train_process(config, gpu_id, result_queue, config_loader):
    """
    训练进程函数

    参数:
        config: 模型配置（从YAML加载的完整配置）
        gpu_id: GPU ID
        result_queue: 结果队列，用于收集训练结果
        config_loader: 配置加载器实例
    """
    try:
        # 设置设备
        device = torch.device(f"cuda:{gpu_id}" if torch.cuda.is_available() else "cpu")

        # 等待GPU显存
        required_memory = config['resources']['estimated_memory']
        wait_for_gpu_memory(required_memory)

        # 获取配置信息
        model_name = f"FasterNet_{config['name']}"
        transform = config['transform']
        hyperparam_config = config['hyperparameter_config_data']
        batch_size = config['resources']['batch_size']

        # 获取输入尺寸
        transform_data = config['transform_config_data']
        input_size = transform_data['input_size'][0]  # [224, 224] -> 224

        # 创建结果目录
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        result_dir = os.path.join("results", model_name, timestamp)
        os.makedirs(result_dir, exist_ok=True)

        # 获取训练前的GPU信息
        gpu_info_before = get_detailed_gpu_info()
        if gpu_info_before:
            print(f"GPU Status Before Training: {gpu_info_before['used_gb']:.1f}GB/{gpu_info_before['total_gb']:.1f}GB ({gpu_info_before['utilization']:.1f}% util)")

        print(f"Starting training model: {model_name}")
        print(f"Description: {config.get('description', 'N/A')}")
        print(f"Input size: {input_size}x{input_size}, Normalization: {'Yes' if transform_data.get('normalize', False) else 'No'}")
        print(f"Batch size: {batch_size}")
        print(f"Hyperparameters: {hyperparam_config['name']} - {hyperparam_config.get('description', '')}")
        print(f"Result directory: {result_dir}")

        # 获取采样策略配置
        sampling_strategy_config = config.get('sampling_strategy_config', {})
        sample_size = config_loader.get_data_sample_size(input_size, config['name'], sampling_strategy_config)
        print(f"Data sampling: {sample_size} images from {config_loader.get_global_settings().get('total_images', 190000):,} total images")

        # 获取训练配置
        training_config = config.get('training_config', {})
        num_epochs = training_config.get('num_epochs', 50)

        # 显示采样策略信息
        if sampling_strategy_config:
            strategy_name = sampling_strategy_config.get('strategy_name', 'unknown')
            strategy_type = sampling_strategy_config.get('strategy_type', 'unknown')
            parameters = sampling_strategy_config.get('parameters', {})

            print(f"🎯 Using sampling strategy: {strategy_name} ({strategy_type})")

            if strategy_type == 'original_based':
                print(f"  - Target originals: {parameters.get('target_originals', 'N/A')}")
                print(f"  - Samples per original: {parameters.get('samples_per_original', 'N/A')}")
                print(f"  - Total samples: {parameters.get('total_samples', 'N/A')}")
                if parameters.get('cross_subset_sampling'):
                    print(f"  - Cross-subset sampling enabled")
            elif strategy_type == 'random':
                print(f"  - Random sampling with seed: {parameters.get('seed', 'N/A')}")
                print(f"  - Sample size: {parameters.get('sample_size', 'N/A')}")
            elif strategy_type in ['balanced', 'stratified']:
                print(f"  - Total samples: {parameters.get('total_samples', 'N/A')}")

        # 向后兼容：获取original采样配置
        original_sampling_config = config.get('original_sampling_config')
        use_original_sampling = (sampling_strategy_config.get('strategy_type') == 'original_based' or
                               ('original' in model_name.lower() and original_sampling_config is not None))

        # 训练模型
        train_model(
            model_config=config['model'],  # 只传递模型参数部分
            hyperparam_config=hyperparam_config,
            transform=transform,
            result_dir=result_dir,
            device=device,
            num_epochs=num_epochs,
            batch_size=batch_size,
            task_name=model_name,
            sample_size=sample_size,
            sampling_strategy_config=sampling_strategy_config,
            original_sampling_config=original_sampling_config,  # 向后兼容
            use_original_sampling=use_original_sampling
        )

        # 获取训练后的GPU信息
        gpu_info_after = get_detailed_gpu_info()
        if gpu_info_after:
            print(f"GPU Status After Training: {gpu_info_after['used_gb']:.1f}GB/{gpu_info_after['total_gb']:.1f}GB ({gpu_info_after['utilization']:.1f}% util)")

        # 将结果放入队列
        result_queue.put({
            'model_name': model_name,
            'config': config,
            'input_size': input_size,
            'result_dir': result_dir,
            'status': 'success'
        })

        print(f"Model {model_name} training completed!")

    except Exception as e:
        # 记录错误
        print(f"Model {config['name']} training failed: {str(e)}")
        result_queue.put({
            'model_name': f"FasterNet_{config['name']}",
            'config': config,
            'input_size': config.get('transform_config_data', {}).get('input_size', [0, 0])[0],
            'status': 'failed',
            'error': str(e)
        })

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='FasterNet多进程训练')
    parser.add_argument('--gpu', type=int, default=0, help='GPU ID')
    parser.add_argument('--max_memory', type=float, default=8.0, help='最大GPU显存限制(GB)')
    parser.add_argument('--sequential', action='store_true', help='顺序训练而非并行训练')
    parser.add_argument('--config', type=str, default=None, help='配置文件路径')
    args = parser.parse_args()

    # 设置全局变量
    global MAX_GPU_MEMORY
    MAX_GPU_MEMORY = args.max_memory

    # 加载配置
    print("🔧 Loading configuration...")
    config_loader = ConfigLoader(args.config)

    # 打印配置摘要
    config_loader.print_config_summary()

    # 获取启用的训练配置
    configs = config_loader.get_enabled_training_configs()

    if not configs:
        print("❌ No enabled training configurations found!")
        print("💡 Please check your configuration file and set enable: true for desired configurations.")
        return

    print(f"\n🚀 Starting training with GPU memory limit: {args.max_memory}GB")
    print(f"📋 Training mode: {'Sequential' if args.sequential else 'Parallel'}")
    print(f"📊 Total configurations to train: {len(configs)}")

    if not args.sequential:
        # 顺序训练模式 - 更安全的GPU显存管理
        results = []
        for i, config in enumerate(configs):
            print(f"\n{'='*60}")
            print(f"Training configuration {i+1}/{len(configs)}: {config['name']}")
            print(f"{'='*60}")

            # 检查GPU显存
            current_usage = get_gpu_memory_usage()
            print(f"Current GPU usage: {current_usage:.2f}GB/{args.max_memory}GB")

            if current_usage > args.max_memory * 0.8:  # 如果使用超过80%，等待
                print("GPU memory usage high, waiting...")
                wait_for_gpu_memory(1.0)

            # 创建结果队列
            result_queue = mp.Queue()

            # 创建并启动进程
            p = mp.Process(target=train_process, args=(config, args.gpu, result_queue, config_loader))
            p.start()
            p.join()

            # 收集结果
            if not result_queue.empty():
                results.append(result_queue.get())
    else:
        # 并行训练模式 - 原有逻辑
        result_queue = mp.Queue()
        processes = []

        for config in configs:
            # 等待GPU显存可用
            required_memory = config['resources']['estimated_memory']
            if not wait_for_gpu_memory(required_memory):
                print(f"Skipping {config['name']} due to insufficient GPU memory")
                continue

            # 创建进程
            p = mp.Process(target=train_process, args=(config, args.gpu, result_queue, config_loader))
            processes.append(p)
            p.start()

            # 短暂延迟，避免同时启动过多进程
            time.sleep(2)

        # 等待所有进程完成
        for p in processes:
            p.join()

        # 收集结果
        results = []
        while not result_queue.empty():
            results.append(result_queue.get())

    # 打印结果摘要
    print(f"\n{'='*80}")
    print("🏆 TRAINING RESULTS SUMMARY")
    print(f"{'='*80}")

    if not results:
        print("❌ No training results to display.")
        return

    for result in results:
        status = "✅ Success" if result['status'] == 'success' else f"❌ Failed: {result.get('error', 'Unknown error')}"
        input_size = result.get('input_size', 0)
        config_data = result.get('config', {})
        transform_data = config_data.get('transform_config_data', {})
        norm_status = "With Normalization" if transform_data.get('normalize', False) else "Without Normalization"
        sampling_strategy = config_data.get('sampling_strategy_config', {})
        sample_size = config_loader.get_data_sample_size(input_size, config_data.get('name'), sampling_strategy)

        print(f"📋 Model: {result['model_name']}")
        print(f"   - Description: {config_data.get('description', 'N/A')}")
        print(f"   - Input: {input_size}x{input_size}, {norm_status}")
        print(f"   - Data: {sample_size:,} samples from {config_loader.get_global_settings().get('total_images', 190000):,} total")
        print(f"   - Hyperparams: {config_data.get('hyperparameter_config', 'N/A')}")
        print(f"   - Status: {status}")
        if result['status'] == 'success':
            print(f"   - Results: {result.get('result_dir', 'N/A')}")
        print()

    # 统计信息
    success_count = sum(1 for r in results if r['status'] == 'success')
    failed_count = len(results) - success_count

    print(f"📊 Summary: {success_count} successful, {failed_count} failed out of {len(results)} total")
    print("🎉 All training completed!")

if __name__ == "__main__":
    main()
