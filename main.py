#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SeedVision v1 - 统一系统管理入口

🌱 SeedVision v1 是一个企业级深度学习训练管理系统
提供完整的训练、测试、维护、部署功能的统一管理界面

使用方法：
python main.py
"""

import sys
import os
import subprocess
from datetime import datetime

# 添加项目根路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.append(project_root)

class Colors:
    RED = '\033[91m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    MAGENTA = '\033[95m'
    CYAN = '\033[96m'
    WHITE = '\033[97m'
    BOLD = '\033[1m'
    END = '\033[0m'

def show_main_menu():
    """显示主菜单"""
    print(f"\n{Colors.BOLD}🌱 SeedVision v1 - 企业级深度学习训练管理系统{Colors.END}")
    print("=" * 80)
    print(f"{Colors.GREEN}🚀 核心功能{Colors.END}")
    print("1. 基础训练")
    print("2. 智能调度训练")
    print("3. 资源预估")
    print("4. 配置管理")
    print()
    print(f"{Colors.CYAN}🧪 测试功能{Colors.END}")
    print("5. 快速测试 (2-3分钟)")
    print("6. 完整测试 (15-30分钟)")
    print()
    print(f"{Colors.YELLOW}🔧 系统维护{Colors.END}")
    print("7. 部署环境检查")
    print("8. 项目独立性验证")
    print("9. 导入路径修复")
    print("10. 项目整理清理")
    print()
    print(f"{Colors.MAGENTA}📊 系统管理{Colors.END}")
    print("11. 生成项目报告")
    print("12. 日志整理")
    print("13. 配置工具测试")
    print()
    print("0. 退出系统")
    print("=" * 80)

def run_script(script_path, args="", description=""):
    """运行脚本"""
    print(f"\n{Colors.BLUE}[RUNNING]{Colors.END} {description}")
    print("=" * 60)
    print(f"执行: python {script_path} {args}")
    print(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("-" * 60)

    try:
        # 构建完整命令
        cmd = [sys.executable, script_path] + (args.split() if args else [])

        # 执行脚本
        result = subprocess.run(
            cmd,
            cwd=project_root,
            capture_output=False,  # 直接显示输出
            text=True
        )

        print("-" * 60)
        if result.returncode == 0:
            print(f"{Colors.GREEN}[SUCCESS]{Colors.END} 执行成功")
        else:
            print(f"{Colors.RED}[FAILED]{Colors.END} 执行失败 (返回码: {result.returncode})")

        return result.returncode == 0

    except Exception as e:
        print(f"{Colors.RED}[ERROR]{Colors.END} 执行异常: {e}")
        return False

def run_module_function(module_path, function_name, description=""):
    """运行模块函数"""
    print(f"\n{Colors.BLUE}[RUNNING]{Colors.END} {description}")
    print("=" * 60)
    print(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("-" * 60)

    try:
        # 动态导入模块
        module = __import__(module_path, fromlist=[function_name])
        func = getattr(module, function_name)

        # 执行函数
        result = func()

        print("-" * 60)
        print(f"{Colors.GREEN}[SUCCESS]{Colors.END} 执行完成")
        return True

    except Exception as e:
        print(f"{Colors.RED}[ERROR]{Colors.END} 执行异常: {e}")
        print("-" * 60)
        return False

# 核心功能
def basic_training():
    """基础训练"""
    return run_module_function(
        "runners.training.main",
        "main",
        "基础训练 - 单个或多个配置的传统训练方式"
    )

def scheduler_training():
    """智能调度训练"""
    return run_module_function(
        "runners.training.main_scheduler",
        "main",
        "智能调度训练 - 高级资源管理和任务调度"
    )

def resource_estimation():
    """资源预估"""
    return run_script(
        "main_scheduler.py",
        "--mode estimate",
        "资源预估 - 分析训练配置的资源需求"
    )

def config_management():
    """配置管理"""
    return run_module_function(
        "tools.config.config_mongo_tool",
        "main",
        "配置管理 - YAML与MongoDB配置同步工具"
    )

# 测试功能
def quick_test():
    """快速测试"""
    return run_script(
        "quick_test.py",
        "quick",
        "快速测试 - 基础功能验证 (2-3分钟)"
    )

def full_test():
    """完整测试"""
    return run_script(
        "tests/runners/run_tests.py",
        "full",
        "完整测试 - 所有功能验证 (15-30分钟)"
    )

# 系统维护功能
def deployment_check():
    """部署环境检查"""
    return run_script(
        "scripts/deployment/deployment_check.py",
        "",
        "部署环境检查 - 检查Python版本、依赖包、项目结构"
    )

def verify_independence():
    """项目独立性验证"""
    return run_script(
        "scripts/deployment/verify_independence.py",
        "",
        "项目独立性验证 - 确保项目完全独立，无外部依赖"
    )

def fix_imports():
    """导入路径修复"""
    return run_script(
        "scripts/maintenance/fix_imports.py",
        "",
        "导入路径修复 - 自动修复硬编码路径，转换为相对路径"
    )

def project_cleanup():
    """项目整理清理"""
    return run_script(
        "scripts/utilities/organize_project.py",
        "--all",
        "项目整理清理 - 清理缓存、整理日志、生成报告"
    )

# 系统管理功能
def generate_report():
    """生成项目报告"""
    return run_script(
        "scripts/utilities/organize_project.py",
        "--report",
        "生成项目报告 - 详细的项目统计和分析报告"
    )

def organize_logs():
    """日志整理"""
    return run_script(
        "scripts/utilities/organize_project.py",
        "--logs",
        "日志整理 - 按日期归档日志文件"
    )

def test_config_tool():
    """配置工具测试"""
    return run_script(
        "scripts/maintenance/test_config_mongo_tool.py",
        "",
        "配置工具测试 - 测试MongoDB配置管理工具功能"
    )

def main():
    """主函数"""
    # 切换到项目根目录
    os.chdir(project_root)

    # 功能映射
    functions = {
        '1': basic_training,
        '2': scheduler_training,
        '3': resource_estimation,
        '4': config_management,
        '5': quick_test,
        '6': full_test,
        '7': deployment_check,
        '8': verify_independence,
        '9': fix_imports,
        '10': project_cleanup,
        '11': generate_report,
        '12': organize_logs,
        '13': test_config_tool
    }

    while True:
        show_main_menu()
        choice = input(f"\n{Colors.BOLD}请选择功能 (0-13): {Colors.END}").strip()

        if choice == '0':
            print(f"\n{Colors.GREEN}🌱 感谢使用 SeedVision v1！{Colors.END}")
            print(f"{Colors.CYAN}💡 系统已为您的深度学习训练提供了完整的管理解决方案{Colors.END}")
            break
        elif choice in functions:
            try:
                success = functions[choice]()
                if success:
                    input(f"\n{Colors.GREEN}✅ 操作完成，按回车键继续...{Colors.END}")
                else:
                    input(f"\n{Colors.YELLOW}⚠️  操作完成但可能有问题，按回车键继续...{Colors.END}")
            except KeyboardInterrupt:
                print(f"\n\n{Colors.YELLOW}[INTERRUPTED]{Colors.END} 操作被用户中断")
                input(f"{Colors.YELLOW}按回车键继续...{Colors.END}")
            except Exception as e:
                print(f"\n{Colors.RED}[ERROR]{Colors.END} 执行异常: {e}")
                input(f"{Colors.RED}按回车键继续...{Colors.END}")
        else:
            print(f"\n{Colors.RED}❌ 无效选择，请输入 0-13 之间的数字{Colors.END}")
            input(f"{Colors.YELLOW}按回车键继续...{Colors.END}")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print(f"\n\n{Colors.YELLOW}程序被用户中断{Colors.END}")
    except Exception as e:
        print(f"\n{Colors.RED}程序异常: {e}{Colors.END}")
        sys.exit(1)
