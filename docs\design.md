# SeedVision v1 系统设计文档

## 版本历史
- **v1.0** (初始版本): 基础方案设计
- **v1.1** (配置系统): 统一配置系统、平衡数据采样、多进程训练
- **v1.2** (跨子集采样): 可配置图片数量、跨子集采样、平衡batch构建
- **v1.3** (当前版本): 数据格式兼容、错误修复、完整测试套件

## 项目概述

SeedVision v1 是一个基于FasterNet的种子图像分析系统，支持可配置的original级别采样、跨子集数据采样、平衡batch构建等高级功能。系统能够从种子图像中预测油含量和蛋白质含量。

### 核心思路
1. **YOLO检测**: 使用预训练的YOLO模型检测图片中的多个油菜籽对象
2. **FasterNet预测**: 对每个检测到的油菜籽使用FasterNet预测油含量和蛋白质含量
3. **结果聚合**: 将多个预测值聚合为最终的样本预测结果

### 当前实现状态
✅ **已完成**:
- 完整的训练框架
- 统一配置管理系统
- 可配置图片数量采样
- 跨子集采样策略（+107.9%可用original）
- 平衡batch构建机制
- 多格式数据兼容性
- 多进程GPU显存管理
- 6种超参数配置
- 6种数据变换配置
- 完整测试套件
- 错误处理和修复机制
- 可视化系统优化（英文标题、文本框位置修复）

🔄 **进行中**:
- 训练效果优化
- 损失函数和R²值调优


## 1. 数据管理系统

### 1.1 数据分布重新设计
**问题**: 原始数据分布极不均衡
- Train: 2,157 (1.1%)
- Val: 271 (0.1%)
- Test: 192,769 (98.8%)

**解决方案**: 按original_image重新分配数据
- **分配比例**: 8:1:1 (Train:Val:Test)
- **分配策略**: 每个original_image的数据按比例分配到各个集合
- **结果**:
  - Train: 155,076 (79.4%)
  - Val: 18,332 (9.4%)
  - Test: 21,789 (11.2%)

### 1.2 可配置图片数量采样
**目标**: 灵活配置每个original的采样数量和总图片数量

**核心功能**:
- **可配置original数量**: 支持任意数量的original（如40个）
- **可配置样本数量**: 每个original可采样任意数量（如60条）
- **总量控制**: 自动计算总样本数（如40×60=2400张）
- **比例分配**: 支持自定义train/val/test比例（默认8:1:1）

### 1.3 跨子集采样策略
**突破性功能**: 无视train/val/test子集边界，从整个数据集中采样

**显著优势**:
- **传统采样**: 仅从训练集采样，1,143个original有足够样本（≥60个）
- **跨子集采样**: 从全部数据采样，2,376个original有足够样本（≥60个）
- **改进效果**: +1,233个original，提升**107.9%**

**实现算法**:
```python
def fixed_sample_by_original_cross_subset(all_data, config):
    # 1. 分析全部数据中的original分布
    original_groups = analyze_original_images(all_data)

    # 2. 筛选有足够样本的original
    valid_originals = filter_by_sample_count(original_groups, min_samples)

    # 3. 随机选择目标数量的original
    selected_originals = random.sample(valid_originals, target_count)

    # 4. 为每个original采样指定数量并按比例分配
    for original in selected_originals:
        samples = random.sample(original_data, samples_per_original)
        train_samples = samples[:train_count]
        val_samples = samples[train_count:train_count+val_count]
        test_samples = samples[train_count+val_count:]

    return train_data, val_data, test_data, mapping
```

### 1.4 平衡Batch构建机制
**目标**: 确保每个batch从每个original平均取1张图片

**实现**:
```python
# 创建平衡的batch采样器
def create_balanced_batch_sampler(original_mapping, batch_size):
    # 每个batch包含来自不同original的样本
    for batch_idx in range(total_batches):
        batch_indices = []
        # 从每个original中取第batch_idx个样本
        for original_name in original_mapping.keys():
            if batch_idx < len(original_indices[original_name]):
                batch_indices.append(original_indices[original_name][batch_idx])
        batch_indices_list.append(batch_indices)
```

**效果**:
- 2,680个唯一original_image
- 每个batch数据分布均匀
- 确保训练数据平衡，提高模型泛化能力
## 2. 统一配置管理系统

### 2.1 配置文件结构 (YAML)
```yaml
# 全局设置
global_settings:
  max_gpu_memory: 8.0
  total_images: 190000
  num_epochs: 50

# 数据采样配置
data_sampling:
  "224x224": 2000
  "112x112": 4500
  "56x56": 6000
  "original": 2400  # Original级别采样

# Original级别采样配置
original_sampling:
  target_originals: 40      # 目标original数量
  samples_per_original: 60  # 每个original采样的图片数量
  total_samples: 2400       # 总采样数量
  cross_subset_sampling: true  # 启用跨子集采样
  train_ratio: 0.8             # 训练集比例
  val_ratio: 0.1               # 验证集比例
  test_ratio: 0.1              # 测试集比例

# 超参数配置 (6种)
hyperparameters:
  conservative: {...}  # 保守配置
  standard: {...}      # 标准配置
  aggressive: {...}    # 激进配置
  fine_tune: {...}     # 精细调优
  adaptive: {...}      # 自适应配置
  stable: {...}        # 稳定配置

# 训练配置 (enable字段控制)
training_configs:
  config_original_224x224:
    enable: true  # ✅ 启用跨子集采样
    name: "original_224x224"

    # 训练参数
    training:
      num_epochs: 100  # 可配置训练轮数

    # Original级别采样配置（覆盖全局配置）
    original_sampling:
      cross_subset_sampling: true
      target_originals: 40
      samples_per_original: 60
      total_samples: 2400
      train_ratio: 0.8
      val_ratio: 0.1
      test_ratio: 0.1

    model: {...}
    resources:
      batch_size: 40  # 应该等于target_originals
      estimated_memory: 1.2
    transform_config: "224x224_norm"
    hyperparameter_config: "conservative"
```

### 2.2 配置管理特点
- **集中管理**: 所有配置在单个YAML文件中
- **灵活控制**: enable字段控制是否训练特定配置
- **可配置采样**: 支持original数量、样本数量、训练轮数配置
- **跨子集采样**: 支持启用/禁用跨子集采样策略
- **比例控制**: 支持自定义train/val/test分配比例
- **类型安全**: 配置加载器验证配置完整性
- **易于扩展**: 支持添加新的超参数和模型配置

### 2.3 多种配置预设
系统提供多种预设配置供选择：

#### 标准配置 (默认启用)
- **名称**: `config_original_224x224`
- **图片数量**: 40个original × 60条 = 2400张图
- **训练轮数**: 100 epochs
- **跨子集采样**: 启用

#### 小规模配置 (可选)
- **名称**: `config_original_112x112_small`
- **图片数量**: 20个original × 30条 = 600张图
- **训练轮数**: 80 epochs
- **适用场景**: 快速测试和验证

#### 大规模配置 (可选)
- **名称**: `config_original_224x224_large`
- **图片数量**: 60个original × 80条 = 4800张图
- **训练轮数**: 120 epochs
- **适用场景**: 高性能训练

## 3. 模型架构设计

### 3.1 多级模型串联
1. **YOLO检测阶段**: 使用预训练YOLO模型检测油菜籽对象
2. **FasterNet预测阶段**: 对每个检测对象预测油含量和蛋白质含量
3. **结果聚合阶段**: 将多个预测值聚合为最终样本预测

### 3.2 FasterNet配置矩阵
| 输入尺寸 | 嵌入维度 | 网络深度 | 估计显存 | Batch Size |
|----------|----------|----------|----------|------------|
| 224×224 | 192 | [3,4,18,3] | 1.2GB | 64 |
| 112×112 | 128 | [2,3,12,2] | 0.8GB | 128 |
| 56×56 | 64 | [2,2,8,2] | 0.5GB | 256 |

### 3.3 数据变换策略
- **6种变换配置**: 3种尺寸 × 2种归一化方式
- **自适应Batch Size**: 根据输入尺寸调整批次大小
- **动态采样**: 根据模型承载能力调整数据采样量

### 3.4 数据格式兼容性
系统支持多种数据格式，自动检测并适配：

#### 字典格式（跨子集采样）
```python
{
    'id': 191401,
    'path': 'path/to/image.jpg',
    'type': 'train',
    'original_image': 'path/to/original.jpg',
    'oil': 42.76,
    'protein': 20.89
}
```

#### 元组格式（传统格式）
```python
# 3元素: (image, oil, protein)
# 4元素: (image, oil, protein, original_image)
```

#### 智能处理机制
- **自动格式检测**: 运行时自动识别数据格式
- **图像加载**: 字典格式自动从文件路径加载图像
- **错误处理**: 图像加载失败时提供fallback机制
- **向后兼容**: 保持对所有现有格式的支持



## 4. 多进程训练系统

### 4.1 GPU显存管理
**智能显存监控**:
- 实时监控GPU显存使用情况
- 8GB显存限制，超限时暂停训练步数少的进程
- 显存可用时自动恢复训练

**进程调度策略**:
```python
# 顺序训练模式（推荐）
python main.py --sequential --max_memory 8.0

# 并行训练模式
python main.py --max_memory 8.0
```

### 4.2 训练监控
**实时显示信息**:
```
Epoch 46/50 - Task: FasterNet_224x224_norm - GPU: 3.2GB/8.0GB
Training: 100%|████████| 47/47 [00:25<00:00, 1.40it/s]
Validation: 100%|████████| 12/12 [00:01<00:00, 4.20it/s]
Training Loss: 0.1234, Validation Loss: 0.1456 - GPU After: 3.8GB/8.0GB
Oil Content - Training R2: 0.8234, Validation R2: 0.7891
Protein Content - Training R2: 0.7654, Validation R2: 0.7321
```

## 5. 超参数配置系统

### 5.1 六种超参数配置
| 配置名称 | 学习率 | 优化器 | 调度器 | 梯度裁剪 | 适用场景 |
|----------|--------|--------|--------|----------|----------|
| **conservative** | 0.0001 | Adam | ReduceLROnPlateau | 1.0 | 初始训练，稳定收敛 |
| **standard** | 0.001 | Adam | StepLR | None | 标准训练 |
| **aggressive** | 0.01 | SGD | CosineAnnealingLR | 0.5 | 快速收敛 |
| **fine_tune** | 0.00001 | AdamW | CosineAnnealingWarmRestarts | None | 精细调优 |
| **adaptive** | 0.001 | RMSprop | ReduceLROnPlateau | 2.0 | 自适应学习 |
| **stable** | 0.0005 | Adam | ExponentialLR | 1.5 | 稳定训练 |

### 5.2 配置分配策略
- **224×224**: conservative (保守配置，适合大模型)
- **112×112**: aggressive (激进配置，模型较小可用大学习率)
- **56×56**: adaptive (自适应配置)

## 6. 评估和可视化系统

### 6.1 评估指标
- **损失函数**: MSE Loss
- **评估指标**: R²、RMSE、MAE、RPD
- **可视化**: 训练曲线、回归散点图、预测对比图

### 6.2 结果输出
- **模型保存**: 最佳模型和最终模型
- **可视化图表**: R²曲线、损失曲线、预测结果图
- **训练日志**: 详细的训练过程记录

## 7. 使用方法

### 7.1 配置管理
```bash
# 查看配置摘要
cd models/SeedVision_v1/config
python config_loader.py

# 修改配置文件
# 编辑 training_config.yaml，设置 enable: true/false
```

### 7.2 训练启动
```bash
# 顺序训练（推荐）
python models/SeedVision_v1/main.py --sequential --max_memory 8.0

# 使用自定义配置
python models/SeedVision_v1/main.py --config path/to/config.yaml --sequential
```

### 7.3 数据重新分配
```bash
# 重新分配数据为8:1:1比例
cd models/SeedVision_v1/tools/myscripts
python redistribute_data.py
```

## 8. 当前问题和解决方案

### 8.1 已解决问题
✅ **数据分布不均衡**: 通过8:1:1重新分配解决
✅ **original_image过度采样**: 通过平衡采样机制解决
✅ **GPU显存管理**: 通过智能监控和进程调度解决
✅ **配置管理复杂**: 通过统一YAML配置系统解决
✅ **可用original数量限制**: 通过跨子集采样提升107.9%
✅ **ZeroDivisionError**: 通过验证集错误处理机制解决
✅ **数据格式不匹配**: 通过多格式兼容性支持解决
✅ **图片数量固定**: 通过可配置采样参数解决
✅ **训练轮数固定**: 通过配置文件中的epoch设置解决
✅ **中文字符显示白框**: 通过英文标题和字体配置解决
✅ **回归图文本框重叠**: 通过智能位置分配解决

### 8.2 待优化问题
🔄 **训练损失过大**: 当前损失值~4.7，需要调优到0.01-1.0范围
🔄 **R²值为负**: 当前R²为负数，需要调优到0.0-1.0范围
🔄 **模型收敛**: 需要进一步优化超参数和数据预处理

## 9. 后期改进方向

### 9.1 短期优化
1. **标签归一化**: 检查油含量和蛋白质含量的数值范围
2. **学习率调优**: 进一步降低学习率
3. **损失函数**: 考虑使用Huber Loss替代MSE

### 9.2 长期扩展
1. **差异化样本处理**: 处理同一样本中的极值对象
2. **RegionCNN集成**: 基于FasterNet增加区域检测机制
3. **模型融合**: 结合多种模型的预测结果
4. **分布式训练**: 支持多GPU和多机训练
5. **自动超参数优化**: 集成超参数搜索算法

## 10. 测试系统

### 10.1 测试套件结构
系统包含完整的测试套件，位于 `test/` 目录：

```
test/
├── test_config.py                    # 配置系统测试
├── test_data_loading.py             # 数据加载功能测试
├── test_cross_subset_sampling.py    # 跨子集采样测试
├── test_validation_fix.py           # 验证集修复测试
├── test_data_format_fix.py          # 数据格式兼容性测试
├── test_visualization_fix.py        # 可视化修复测试
├── test_text_position_fix.py        # 文本框位置修复测试
└── debug_data_format.py             # 数据格式调试工具
```

### 10.2 测试覆盖范围
- ✅ **配置加载和解析**: 验证YAML配置正确读取
- ✅ **跨子集采样功能**: 验证采样算法和数据分配
- ✅ **数据格式兼容性**: 验证字典和元组格式支持
- ✅ **错误处理机制**: 验证各种异常情况的处理
- ✅ **训练流程完整性**: 验证端到端训练流程
- ✅ **可视化系统**: 验证英文标题和字体配置
- ✅ **文本框位置**: 验证回归图文本框不重叠

### 10.3 测试结果
所有测试全部通过，确保系统稳定性：
- 🎯 跨子集采样提升107.9%可用original
- 📊 数据格式自动检测和转换
- 🛡️ 错误处理和fallback机制
- ⚖️ 平衡batch构建和采样
- 🎨 可视化英文标题正确显示
- 📍 回归图文本框位置优化

## 11. 文件结构

### 11.1 项目目录结构
```
models/SeedVision_v1/
├── main.py                          # 主程序入口
├── design.md                        # 系统设计文档（本文档）
├── config/
│   ├── config_loader.py            # 配置加载器
│   └── training_config.yaml        # 主配置文件
├── tools/
│   └── myscripts/
│       ├── load_data.py            # 数据加载和采样
│       ├── train.py                # 训练系统
│       └── validate.py             # 验证和评估
├── test/                           # 测试套件
│   ├── test_config.py
│   ├── test_data_loading.py
│   ├── test_cross_subset_sampling.py
│   ├── test_validation_fix.py
│   ├── test_data_format_fix.py
│   └── debug_data_format.py
└── results/                        # 训练结果输出
    ├── models/                     # 保存的模型
    ├── plots/                      # 可视化图表
    └── logs/                       # 训练日志
```

### 11.2 核心文件说明
- **main.py**: 主控制器，管理训练进程和GPU内存
- **config_loader.py**: 统一配置管理，支持YAML解析和验证
- **load_data.py**: 数据加载核心，包含跨子集采样算法
- **train.py**: 训练系统核心，包含多格式数据集类
- **training_config.yaml**: 主配置文件，包含所有训练参数

## 12. 使用指南

### 12.1 快速开始
```bash
# 1. 测试系统状态
python test/test_config.py
python test/test_data_loading.py

# 2. 开始训练
python main.py --sequential --max_memory 8.0
```

### 12.2 自定义配置
1. 编辑 `config/training_config.yaml`
2. 修改 `original_sampling` 参数
3. 设置 `cross_subset_sampling: true`
4. 配置合适的 `batch_size` 和 `num_epochs`

### 12.3 监控训练
- 观察GPU内存使用情况
- 检查数据采样统计信息
- 监控训练和验证指标
- 查看 `results/` 目录中的输出

---

**🚀 SeedVision v1 提供了完整的、可配置的、高性能的种子图像分析解决方案！**
