#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理工具 - YAML配置与MongoDB同步

功能：
1. 将YAML训练配置导入到MongoDB
2. 从MongoDB导出配置到YAML
3. 配置对比和同步
4. 配置备份和恢复

使用方法：
从main.py调用，或直接运行此脚本
"""

import sys
import os
import yaml
import json
from datetime import datetime

# 添加项目根路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(project_root)

# 颜色定义
class Colors:
    RED = '\033[91m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    MAGENTA = '\033[95m'
    CYAN = '\033[96m'
    WHITE = '\033[97m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'
    END = '\033[0m'

class ConfigMongoTool:
    """配置MongoDB管理工具"""

    def __init__(self):
        # 获取项目根目录
        self.project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        self.yaml_config_path = os.path.join(self.project_root, "config", "training_config.yaml")
        self.db_name = "seedvision_configs"
        self.collection_name = "training_configs"

    def load_yaml_config(self):
        """加载YAML配置文件"""
        try:
            with open(self.yaml_config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            return config
        except Exception as e:
            print(f"{Colors.RED}[ERROR]{Colors.END} 无法加载YAML配置: {e}")
            return None

    def get_mongo_connection(self):
        """获取MongoDB连接"""
        try:
            from utils.db_utils import database_connecter
            db_conn = database_connecter
            if db_conn is None:
                print(f"{Colors.RED}[ERROR]{Colors.END} 无法获取数据库连接")
                return None, None

            mongo_db = db_conn.get_mongodb_database(self.db_name)
            collection = mongo_db[self.collection_name]
            return mongo_db, collection
        except Exception as e:
            print(f"{Colors.RED}[ERROR]{Colors.END} MongoDB连接失败: {e}")
            return None, None

    def import_yaml_to_mongo(self):
        """将YAML配置导入到MongoDB"""
        print(f"\n{Colors.BLUE}[IMPORT]{Colors.END} 开始导入YAML配置到MongoDB")
        print("-" * 60)

        # 加载YAML配置
        yaml_config = self.load_yaml_config()
        if not yaml_config:
            return False

        # 获取MongoDB连接
        mongo_db, collection = self.get_mongo_connection()
        if not collection:
            return False

        # 提取训练配置
        training_configs = yaml_config.get('training_configs', {})
        if not training_configs:
            print(f"{Colors.YELLOW}[WARNING]{Colors.END} 没有找到训练配置")
            return False

        print(f"找到 {len(training_configs)} 个配置")

        # 导入配置
        imported_count = 0
        updated_count = 0

        for config_name, config_data in training_configs.items():
            try:
                # 准备文档
                doc = {
                    'config_name': config_name,
                    'config_data': config_data,
                    'imported_at': datetime.now(),
                    'source': 'yaml_import',
                    'version': 1
                }

                # 检查是否已存在
                existing = collection.find_one({'config_name': config_name})

                if existing:
                    # 更新现有配置
                    doc['version'] = existing.get('version', 1) + 1
                    doc['updated_at'] = datetime.now()
                    collection.replace_one({'config_name': config_name}, doc)
                    updated_count += 1
                    print(f"  {Colors.YELLOW}[UPDATED]{Colors.END} {config_name}")
                else:
                    # 插入新配置
                    collection.insert_one(doc)
                    imported_count += 1
                    print(f"  {Colors.GREEN}[IMPORTED]{Colors.END} {config_name}")

            except Exception as e:
                print(f"  {Colors.RED}[FAILED]{Colors.END} {config_name}: {e}")

        print(f"\n{Colors.GREEN}[SUCCESS]{Colors.END} 导入完成")
        print(f"新增: {imported_count}, 更新: {updated_count}")
        return True

    def export_mongo_to_yaml(self):
        """从MongoDB导出配置到YAML"""
        print(f"\n{Colors.BLUE}[EXPORT]{Colors.END} 开始从MongoDB导出配置")
        print("-" * 60)

        # 获取MongoDB连接
        mongo_db, collection = self.get_mongo_connection()
        if not collection:
            return False

        # 查询所有配置
        configs = list(collection.find({}))
        if not configs:
            print(f"{Colors.YELLOW}[WARNING]{Colors.END} MongoDB中没有配置数据")
            return False

        print(f"找到 {len(configs)} 个配置")

        # 构建YAML结构
        yaml_structure = {
            'training_configs': {}
        }

        for config_doc in configs:
            config_name = config_doc['config_name']
            config_data = config_doc['config_data']
            yaml_structure['training_configs'][config_name] = config_data
            print(f"  {Colors.GREEN}[EXPORTED]{Colors.END} {config_name}")

        # 保存到文件
        config_dir = os.path.join(self.project_root, "config")
        backup_path = os.path.join(config_dir, f"training_config_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.yaml")
        export_path = os.path.join(config_dir, "training_config_from_mongo.yaml")

        try:
            # 备份原文件
            if os.path.exists(self.yaml_config_path):
                with open(self.yaml_config_path, 'r', encoding='utf-8') as f:
                    original_config = f.read()
                with open(backup_path, 'w', encoding='utf-8') as f:
                    f.write(original_config)
                print(f"  {Colors.BLUE}[BACKUP]{Colors.END} 原配置已备份到: {backup_path}")

            # 保存导出的配置
            with open(export_path, 'w', encoding='utf-8') as f:
                yaml.dump(yaml_structure, f, default_flow_style=False, allow_unicode=True, indent=2)

            print(f"\n{Colors.GREEN}[SUCCESS]{Colors.END} 导出完成")
            print(f"导出文件: {export_path}")
            return True

        except Exception as e:
            print(f"{Colors.RED}[ERROR]{Colors.END} 保存文件失败: {e}")
            return False

    def list_mongo_configs(self):
        """列出MongoDB中的配置"""
        print(f"\n{Colors.BLUE}[LIST]{Colors.END} MongoDB中的配置列表")
        print("-" * 60)

        # 获取MongoDB连接
        mongo_db, collection = self.get_mongo_connection()
        if not collection:
            return False

        # 查询配置
        configs = list(collection.find({}, {'config_name': 1, 'imported_at': 1, 'version': 1}))

        if not configs:
            print(f"{Colors.YELLOW}[INFO]{Colors.END} MongoDB中没有配置数据")
            return True

        print(f"{'序号':<4} {'配置名':<35} {'版本':<6} {'导入时间':<20}")
        print("-" * 70)

        for i, config in enumerate(configs, 1):
            config_name = config['config_name']
            version = config.get('version', 1)
            imported_at = config.get('imported_at', 'Unknown')
            if isinstance(imported_at, datetime):
                imported_at = imported_at.strftime('%Y-%m-%d %H:%M:%S')

            print(f"{i:<4} {config_name:<35} {version:<6} {str(imported_at):<20}")

        print(f"\n总计: {len(configs)} 个配置")
        return True

    def compare_yaml_mongo(self):
        """对比YAML和MongoDB中的配置"""
        print(f"\n{Colors.BLUE}[COMPARE]{Colors.END} 对比YAML和MongoDB配置")
        print("-" * 60)

        # 加载YAML配置
        yaml_config = self.load_yaml_config()
        if not yaml_config:
            return False

        yaml_configs = yaml_config.get('training_configs', {})

        # 获取MongoDB配置
        mongo_db, collection = self.get_mongo_connection()
        if not collection:
            return False

        mongo_configs = {}
        for doc in collection.find({}):
            mongo_configs[doc['config_name']] = doc['config_data']

        # 对比分析
        yaml_only = set(yaml_configs.keys()) - set(mongo_configs.keys())
        mongo_only = set(mongo_configs.keys()) - set(yaml_configs.keys())
        common = set(yaml_configs.keys()) & set(mongo_configs.keys())

        print(f"YAML配置数量: {len(yaml_configs)}")
        print(f"MongoDB配置数量: {len(mongo_configs)}")
        print(f"共同配置: {len(common)}")
        print(f"仅在YAML中: {len(yaml_only)}")
        print(f"仅在MongoDB中: {len(mongo_only)}")

        if yaml_only:
            print(f"\n{Colors.YELLOW}[YAML ONLY]{Colors.END}")
            for config in sorted(yaml_only):
                print(f"  - {config}")

        if mongo_only:
            print(f"\n{Colors.CYAN}[MONGO ONLY]{Colors.END}")
            for config in sorted(mongo_only):
                print(f"  - {config}")

        # 检查内容差异
        different_configs = []
        for config_name in common:
            if yaml_configs[config_name] != mongo_configs[config_name]:
                different_configs.append(config_name)

        if different_configs:
            print(f"\n{Colors.MAGENTA}[DIFFERENT CONTENT]{Colors.END}")
            for config in sorted(different_configs):
                print(f"  - {config}")

        return True

    def sync_training_results_to_mongo(self):
        """同步训练结果到MongoDB"""
        print(f"\n{Colors.BLUE}[SYNC]{Colors.END} 同步训练结果到MongoDB")
        print("-" * 60)

        # 获取MongoDB连接
        mongo_db, collection = self.get_mongo_connection()
        if not collection:
            return False

        results_collection = mongo_db['training_results']

        # 扫描训练结果目录
        results_dir = os.path.join(self.project_root, "output", "training", "results")
        if not os.path.exists(results_dir):
            print(f"{Colors.YELLOW}[WARNING]{Colors.END} 训练结果目录不存在: {results_dir}")
            return False

        synced_count = 0

        for config_dir in os.listdir(results_dir):
            config_path = os.path.join(results_dir, config_dir)
            if not os.path.isdir(config_path):
                continue

            # 查找训练参数文件
            params_dir = os.path.join(config_path, "training_params")
            if not os.path.exists(params_dir):
                continue

            # 获取最新的参数文件
            param_files = [f for f in os.listdir(params_dir) if f.endswith('.json')]
            if not param_files:
                continue

            latest_param_file = sorted(param_files)[-1]
            param_file_path = os.path.join(params_dir, latest_param_file)

            try:
                with open(param_file_path, 'r', encoding='utf-8') as f:
                    training_result = json.load(f)

                # 添加元数据
                result_doc = {
                    'config_name': config_dir,
                    'training_result': training_result,
                    'synced_at': datetime.now(),
                    'result_path': config_path,
                    'param_file': latest_param_file
                }

                # 检查是否已存在
                existing = results_collection.find_one({'config_name': config_dir})

                if existing:
                    results_collection.replace_one({'config_name': config_dir}, result_doc)
                    print(f"  {Colors.YELLOW}[UPDATED]{Colors.END} {config_dir}")
                else:
                    results_collection.insert_one(result_doc)
                    print(f"  {Colors.GREEN}[SYNCED]{Colors.END} {config_dir}")

                synced_count += 1

            except Exception as e:
                print(f"  {Colors.RED}[FAILED]{Colors.END} {config_dir}: {e}")

        print(f"\n{Colors.GREEN}[SUCCESS]{Colors.END} 同步完成，处理了 {synced_count} 个结果")
        return True

    def clear_mongo_configs(self):
        """清空MongoDB中的配置"""
        print(f"\n{Colors.YELLOW}[WARNING]{Colors.END} 即将清空MongoDB中的所有配置")
        confirm = input("确认清空？(输入 'YES' 确认): ").strip()

        if confirm != 'YES':
            print(f"{Colors.BLUE}[CANCELLED]{Colors.END} 操作已取消")
            return False

        # 获取MongoDB连接
        mongo_db, collection = self.get_mongo_connection()
        if not collection:
            return False

        try:
            result = collection.delete_many({})
            print(f"{Colors.GREEN}[SUCCESS]{Colors.END} 已删除 {result.deleted_count} 个配置")
            return True
        except Exception as e:
            print(f"{Colors.RED}[ERROR]{Colors.END} 清空失败: {e}")
            return False

def show_config_menu():
    """显示配置管理菜单"""
    print("\n" + "=" * 60)
    print("配置管理工具 - YAML ↔ MongoDB")
    print("=" * 60)
    print("1. 导入YAML配置到MongoDB")
    print("2. 从MongoDB导出配置到YAML")
    print("3. 列出MongoDB中的配置")
    print("4. 对比YAML和MongoDB配置")
    print("5. 同步训练结果到MongoDB")
    print("6. 清空MongoDB配置")
    print("7. 返回主菜单")
    print("-" * 60)

def main():
    """主函数"""
    tool = ConfigMongoTool()

    while True:
        show_config_menu()
        choice = input("请选择功能 (1-7): ").strip()

        if choice == "1":
            tool.import_yaml_to_mongo()
        elif choice == "2":
            tool.export_mongo_to_yaml()
        elif choice == "3":
            tool.list_mongo_configs()
        elif choice == "4":
            tool.compare_yaml_mongo()
        elif choice == "5":
            tool.sync_training_results_to_mongo()
        elif choice == "6":
            tool.clear_mongo_configs()
        elif choice == "7":
            print("返回主菜单")
            break
        else:
            print("无效选择，请重新输入")

        input("\n按回车键继续...")

if __name__ == "__main__":
    main()
