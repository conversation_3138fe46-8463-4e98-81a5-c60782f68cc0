'''
数据库相关操作，在这里配置，有些数据保存到数据库更方便
支持 MySQL 和 MongoDB 连接
'''

import pymysql
from pymongo import MongoClient
from pymongo.errors import ConnectionFailure, ServerSelectionTimeoutError

def test_connection():
    try:
        # 连接数据库
        connection = pymysql.connect(
            host='127.0.0.1',  # 数据库主机地址
            user='root',       # 数据库用户名
            password='911711', # 数据库密码
            charset='utf8mb4', # 字符集
            cursorclass=pymysql.cursors.DictCursor # 返回字典格式
        )

        # show
        with connection.cursor() as cursor:
            # 执行查询
            cursor.execute("SHOW DATABASES;")
            # 获取查询结果
            result = cursor.fetchall()
            print(result)

        # 关闭连接
        connection.close()
    except pymysql.Error as e:
        print(f"Error: {e}")

class DBConnection:
    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(DBConnection, cls).__new__(cls)
            cls._instance._create_connections()
        return cls._instance

    def _create_connections(self):
        """创建 MySQL 和 MongoDB 连接"""
        # MySQL 连接
        try:
            self.mysql_connection = pymysql.connect(
                host='127.0.0.1',
                user='root',
                password='911711',
                charset='utf8mb4',
                cursorclass=pymysql.cursors.DictCursor
            )
            print("MySQL 连接成功")
        except pymysql.Error as e:
            print(f"MySQL 连接失败: {e}")
            self.mysql_connection = None

        # MongoDB 连接
        try:
            host = '127.0.0.1'
            port = 27017
            username = 'zhifu'
            password = '911711'

            connection_string = f"mongodb://{username}:{password}@{host}:{port}/"

            self.mongodb_client = MongoClient(
                connection_string,
                serverSelectionTimeoutMS=5000,
                connectTimeoutMS=5000,
                socketTimeoutMS=5000
            )

            # 测试 MongoDB 连接
            self.mongodb_client.admin.command('ping')
            print("MongoDB 连接成功")

        except (ConnectionFailure, ServerSelectionTimeoutError) as e:
            print(f"MongoDB 连接失败: {e}")
            self.mongodb_client = None
        except Exception as e:
            print(f"MongoDB 连接异常: {e}")
            self.mongodb_client = None

    def get_mysql_connection(self):
        """获取 MySQL 连接"""
        return self.mysql_connection

    def get_mongodb_database(self, db_name):
        """获取 MongoDB 数据库"""
        if self.mongodb_client is None:
            raise Exception("MongoDB 连接未建立")
        return self.mongodb_client[db_name]

    def list_mongodb_databases(self):
        """列出所有 MongoDB 数据库"""
        if self.mongodb_client is None:
            raise Exception("MongoDB 连接未建立")
        return self.mongodb_client.list_database_names()

    def close_connections(self):
        """关闭所有连接"""
        if hasattr(self, 'mysql_connection') and self.mysql_connection:
            self.mysql_connection.close()
            print("MySQL 连接已关闭")

        if hasattr(self, 'mongodb_client') and self.mongodb_client:
            self.mongodb_client.close()
            print("MongoDB 连接已关闭")

def test_mongodb_connection():
    """测试 MongoDB 连接"""
    try:
        # 获取数据库连接实例
        db_conn = DBConnection()

        # 列出 MongoDB 数据库
        databases = db_conn.list_mongodb_databases()
        print(f"可用 MongoDB 数据库: {databases}")

        # 测试获取数据库
        test_db = db_conn.get_mongodb_database('test')
        print(f"获取测试数据库: {test_db.name}")

        return True

    except Exception as e:
        print(f"MongoDB 连接测试失败: {e}")
        return False

# 创建单例连接实例
db_connection = DBConnection()

# 向后兼容的接口
mysql_connecter = db_connection.get_mysql_connection()

# MongoDB 连接器接口
def get_mongodb_database(db_name):
    """获取 MongoDB 数据库"""
    return db_connection.get_mongodb_database(db_name)

def list_mongodb_databases():
    """列出所有 MongoDB 数据库"""
    return db_connection.list_mongodb_databases()

# 统一的数据库连接器
database_connecter = db_connection
