# 🌱 SeedVisionTrain - 独立种子视觉分析训练系统

[![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://python.org)
[![PyTorch](https://img.shields.io/badge/PyTorch-1.9+-red.svg)](https://pytorch.org)
[![Status](https://img.shields.io/badge/Status-Independent-brightgreen.svg)]()

## 🎯 项目概述

SeedVisionTrain 是一个完全独立的种子视觉分析训练系统，基于SeedVision v1项目重构而成。本项目集成了所有必要的组件，无需依赖外部项目路径，可以在任何环境中独立运行。

### ✨ 独立性特性

- **🔒 完全自包含**: 集成了所有utils组件，无外部依赖
- **📁 相对路径**: 所有导入使用相对路径，支持任意目录部署
- **🚀 即插即用**: 复制到任何位置都能直接运行
- **🔧 统一配置**: 所有配置文件都在项目内部

## 📦 项目结构

```
SeedVisionTrain/
├── main.py                           # 🚪 主入口 (统一菜单)
├── README_INDEPENDENT.md             # 📖 独立项目说明
├── verify_independence.py            # 🔍 独立性验证脚本
├── fix_imports.py                    # 🔧 导入修复工具
│
├── utils/                            # 🛠️ 内置工具模块
│   ├── __init__.py                   # 模块初始化
│   ├── logger.py                     # 日志系统
│   ├── db_utils.py                   # 数据库连接 (MySQL + MongoDB)
│   └── path_manage.py                # 路径管理
│
├── config/                           # ⚙️ 配置系统
│   ├── config_loader.py              # 配置加载器
│   ├── training_config.yaml          # 32个训练配置
│   └── output_config.py              # 输出路径管理
│
├── models/                           # 🧠 模型定义
│   ├── FasterNet.py                  # FasterNet模型
│   ├── Mixed_YOLO_FasterNet.py       # 混合模型
│   └── fasternet_blocks.py           # 模型组件
│
├── tools/                            # 🔧 核心工具
│   ├── training/                     # 训练工具
│   │   ├── train.py                  # 核心训练逻辑
│   │   ├── validate.py               # 验证评估
│   │   └── visualize.py              # 可视化
│   ├── data/                         # 数据处理
│   ├── config/                       # 配置管理
│   │   └── config_mongo_tool.py      # YAML-MongoDB同步
│   └── analysis/                     # 分析工具
│
├── scheduler/                        # 📋 智能调度
│   ├── task_scheduler.py             # 任务调度器
│   ├── enhanced_scheduler.py         # 增强调度器
│   └── process_manager.py            # 进程管理
│
├── runners/                          # 🏃 执行脚本
│   └── training/                     # 训练执行器
│       ├── main.py                   # 基础训练
│       └── main_scheduler.py         # 调度训练
│
├── tests/                            # 🧪 测试系统
│   └── runners/                      # 测试执行器
│       └── system_test.py            # 系统测试
│
└── output/                           # 📊 输出目录
    ├── training/                     # 训练结果
    └── testing/                      # 测试结果
```

## 🚀 快速开始

### 1. 环境要求

```bash
Python >= 3.8
PyTorch >= 1.9
CUDA >= 11.0 (可选，用于GPU训练)
```

### 2. 依赖安装

```bash
pip install torch torchvision torchaudio
pip install numpy pandas matplotlib tqdm
pip install pillow opencv-python
pip install pymongo pymysql
pip install yaml ultralytics GPUtil
```

### 3. 验证独立性

```bash
# 验证项目是否完全独立
python verify_independence.py
```

### 4. 启动系统

```bash
# 启动主系统
python main.py

# 选择功能:
# 1. 基础训练 (推荐)
# 2. 智能调度训练 (高级)
# 3. 配置管理工具
# 4. 退出
```

## 🎛️ 功能模块

### 1. 基础训练系统
- 32个预配置的训练任务
- 多种图像尺寸 (224x224, 112x112, 56x56)
- 不同的数据增强策略
- 自动结果保存和可视化

### 2. 智能调度系统
- GPU内存智能管理 (8GB限制)
- 任务优先级调度
- 失败任务自动重试
- 资源使用优化

### 3. 配置管理工具
- YAML配置导入到MongoDB
- MongoDB配置导出到YAML
- 配置差异对比分析
- 训练结果自动同步

### 4. 数据库支持
- MySQL连接管理
- MongoDB文档存储
- 统一的数据库接口
- 自动连接重试

## 🔧 独立性保证

### 路径管理
```python
# 所有文件都使用相对路径计算
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(project_root)
```

### 组件集成
- ✅ `utils/` - 完整集成原外部utils组件
- ✅ `config/` - 所有配置文件内置
- ✅ `models/` - 模型定义完全独立
- ✅ `tools/` - 工具函数自包含

### 验证工具
- `verify_independence.py` - 检查项目独立性
- `fix_imports.py` - 自动修复导入路径

## 📊 使用示例

### 基础训练
```bash
python main.py
# 选择 "1. 基础训练"
# 系统将自动运行所有启用的配置
```

### 配置管理
```bash
python main.py
# 选择 "3. 配置管理工具"
# 可以进行YAML-MongoDB同步操作
```

### 直接调用
```python
from tools.training.train import train_model
from config.config_loader import ConfigLoader

# 加载配置
config_loader = ConfigLoader()
configs = config_loader.get_enabled_configs()

# 训练模型
for config_name, config in configs.items():
    train_model(**config)
```

## 🔍 故障排除

### 导入错误
```bash
# 运行导入修复工具
python fix_imports.py

# 验证修复结果
python verify_independence.py
```

### 路径问题
确保所有Python文件都在项目根目录下运行：
```bash
cd /path/to/SeedVisionTrain
python main.py
```

### 数据库连接
检查MongoDB和MySQL服务是否运行：
```bash
# 测试数据库连接
python -c "from utils.db_utils import test_mongodb_connection; test_mongodb_connection()"
```

## 📝 更新日志

### v1.0 - 独立版本
- ✅ 完全独立的项目结构
- ✅ 集成所有外部依赖
- ✅ 相对路径导入系统
- ✅ 独立性验证工具
- ✅ 配置管理工具集成

## 🤝 贡献指南

1. 保持项目独立性 - 不添加外部路径依赖
2. 使用相对路径 - 所有导入使用项目内路径
3. 测试独立性 - 每次修改后运行验证脚本
4. 文档更新 - 及时更新README和文档

## 📄 许可证

本项目基于MIT许可证开源。

---

**🎉 现在您拥有了一个完全独立的SeedVisionTrain系统！**
