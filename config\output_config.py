#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
输出目录配置管理

统一管理所有输出文件的目录结构
"""

import os
from datetime import datetime
from typing import Dict, Optional

class OutputConfig:
    """输出目录配置管理器"""
    
    def __init__(self, base_dir: str = "output"):
        """
        初始化输出配置
        
        参数:
            base_dir: 基础输出目录
        """
        self.base_dir = base_dir
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 定义标准目录结构
        self.directories = {
            # 训练相关输出
            'training': {
                'models': os.path.join(base_dir, 'training', 'models'),
                'results': os.path.join(base_dir, 'training', 'results'),
                'logs': os.path.join(base_dir, 'training', 'logs'),
                'visualizations': os.path.join(base_dir, 'training', 'visualizations'),
                'checkpoints': os.path.join(base_dir, 'training', 'checkpoints')
            },
            
            # 测试相关输出
            'testing': {
                'results': os.path.join(base_dir, 'testing', 'results'),
                'logs': os.path.join(base_dir, 'testing', 'logs'),
                'visualizations': os.path.join(base_dir, 'testing', 'visualizations'),
                'reports': os.path.join(base_dir, 'testing', 'reports')
            },
            
            # 调度器相关输出
            'scheduler': {
                'logs': os.path.join(base_dir, 'scheduler', 'logs'),
                'reports': os.path.join(base_dir, 'scheduler', 'reports'),
                'tasks': os.path.join(base_dir, 'scheduler', 'tasks')
            },
            
            # 分析相关输出
            'analysis': {
                'data': os.path.join(base_dir, 'analysis', 'data'),
                'visualizations': os.path.join(base_dir, 'analysis', 'visualizations'),
                'reports': os.path.join(base_dir, 'analysis', 'reports')
            },
            
            # 临时文件
            'temp': {
                'configs': os.path.join(base_dir, 'temp', 'configs'),
                'cache': os.path.join(base_dir, 'temp', 'cache'),
                'logs': os.path.join(base_dir, 'temp', 'logs')
            }
        }
    
    def get_path(self, category: str, subcategory: str, filename: Optional[str] = None, 
                 task_name: Optional[str] = None, create_dirs: bool = True) -> str:
        """
        获取标准化的文件路径
        
        参数:
            category: 主分类 (training, testing, scheduler, analysis, temp)
            subcategory: 子分类 (models, results, logs, visualizations等)
            filename: 文件名 (可选)
            task_name: 任务名称 (可选，用于创建子目录)
            create_dirs: 是否自动创建目录
            
        返回:
            完整的文件路径
        """
        if category not in self.directories:
            raise ValueError(f"Unknown category: {category}")
        
        if subcategory not in self.directories[category]:
            raise ValueError(f"Unknown subcategory: {subcategory} for category: {category}")
        
        # 获取基础路径
        base_path = self.directories[category][subcategory]
        
        # 添加任务名称子目录
        if task_name:
            base_path = os.path.join(base_path, task_name)
        
        # 创建目录
        if create_dirs:
            os.makedirs(base_path, exist_ok=True)
        
        # 添加文件名
        if filename:
            return os.path.join(base_path, filename)
        else:
            return base_path
    
    def get_training_paths(self, task_name: str) -> Dict[str, str]:
        """
        获取训练相关的所有路径
        
        参数:
            task_name: 任务名称
            
        返回:
            包含所有训练路径的字典
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        return {
            'model_dir': self.get_path('training', 'models', task_name=task_name),
            'result_dir': self.get_path('training', 'results', task_name=task_name),
            'log_dir': self.get_path('training', 'logs', task_name=task_name),
            'viz_dir': self.get_path('training', 'visualizations', task_name=task_name),
            'checkpoint_dir': self.get_path('training', 'checkpoints', task_name=task_name),
            
            # 具体文件路径
            'model_file': self.get_path('training', 'models', f'{task_name}_model_{timestamp}.pth', task_name),
            'best_model_file': self.get_path('training', 'models', f'{task_name}_best_model_{timestamp}.pth', task_name),
            'log_file': self.get_path('training', 'logs', f'{task_name}_training_{timestamp}.log', task_name),
            'loss_curve': self.get_path('training', 'visualizations', f'{task_name}_loss_curve_{timestamp}.png', task_name),
            'protein_plot': self.get_path('training', 'visualizations', f'{task_name}_protein_regression_{timestamp}.png', task_name),
            'oil_plot': self.get_path('training', 'visualizations', f'{task_name}_oil_regression_{timestamp}.png', task_name),
            'results_json': self.get_path('training', 'results', f'{task_name}_results_{timestamp}.json', task_name)
        }
    
    def get_testing_paths(self, test_name: str) -> Dict[str, str]:
        """
        获取测试相关的所有路径
        
        参数:
            test_name: 测试名称
            
        返回:
            包含所有测试路径的字典
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        return {
            'result_dir': self.get_path('testing', 'results', task_name=test_name),
            'log_dir': self.get_path('testing', 'logs', task_name=test_name),
            'viz_dir': self.get_path('testing', 'visualizations', task_name=test_name),
            'report_dir': self.get_path('testing', 'reports', task_name=test_name),
            
            # 具体文件路径
            'log_file': self.get_path('testing', 'logs', f'{test_name}_test_{timestamp}.log', test_name),
            'report_file': self.get_path('testing', 'reports', f'{test_name}_report_{timestamp}.json', test_name),
            'viz_file': self.get_path('testing', 'visualizations', f'{test_name}_visualization_{timestamp}.png', test_name)
        }
    
    def get_scheduler_paths(self, scheduler_name: str = "main") -> Dict[str, str]:
        """
        获取调度器相关的所有路径
        
        参数:
            scheduler_name: 调度器名称
            
        返回:
            包含所有调度器路径的字典
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        return {
            'log_dir': self.get_path('scheduler', 'logs', task_name=scheduler_name),
            'report_dir': self.get_path('scheduler', 'reports', task_name=scheduler_name),
            'task_dir': self.get_path('scheduler', 'tasks', task_name=scheduler_name),
            
            # 具体文件路径
            'scheduler_log': self.get_path('scheduler', 'logs', f'{scheduler_name}_scheduler_{timestamp}.log', scheduler_name),
            'task_log': self.get_path('scheduler', 'tasks', f'{scheduler_name}_tasks_{timestamp}.log', scheduler_name),
            'report_file': self.get_path('scheduler', 'reports', f'{scheduler_name}_report_{timestamp}.json', scheduler_name)
        }
    
    def cleanup_temp_files(self, older_than_hours: int = 24):
        """
        清理临时文件
        
        参数:
            older_than_hours: 清理多少小时前的文件
        """
        import time
        
        temp_dirs = [
            self.directories['temp']['configs'],
            self.directories['temp']['cache'],
            self.directories['temp']['logs']
        ]
        
        cutoff_time = time.time() - (older_than_hours * 3600)
        
        for temp_dir in temp_dirs:
            if os.path.exists(temp_dir):
                for filename in os.listdir(temp_dir):
                    file_path = os.path.join(temp_dir, filename)
                    if os.path.isfile(file_path):
                        if os.path.getmtime(file_path) < cutoff_time:
                            try:
                                os.remove(file_path)
                                print(f"Cleaned up temp file: {file_path}")
                            except Exception as e:
                                print(f"Failed to clean up {file_path}: {e}")
    
    def create_all_directories(self):
        """创建所有标准目录"""
        for category, subcategories in self.directories.items():
            for subcategory, path in subcategories.items():
                os.makedirs(path, exist_ok=True)
                print(f"Created directory: {path}")
    
    def get_directory_structure(self) -> Dict:
        """获取完整的目录结构"""
        return self.directories.copy()
    
    def print_structure(self):
        """打印目录结构"""
        print("📁 SeedVision v1 Output Directory Structure")
        print("=" * 50)
        
        for category, subcategories in self.directories.items():
            print(f"\n📂 {category.upper()}")
            for subcategory, path in subcategories.items():
                print(f"   📁 {subcategory}: {path}")

# 创建全局输出配置实例
output_config = OutputConfig()

def get_output_config() -> OutputConfig:
    """获取全局输出配置实例"""
    return output_config

# 便捷函数
def get_training_paths(task_name: str) -> Dict[str, str]:
    """获取训练路径的便捷函数"""
    return output_config.get_training_paths(task_name)

def get_testing_paths(test_name: str) -> Dict[str, str]:
    """获取测试路径的便捷函数"""
    return output_config.get_testing_paths(test_name)

def get_scheduler_paths(scheduler_name: str = "main") -> Dict[str, str]:
    """获取调度器路径的便捷函数"""
    return output_config.get_scheduler_paths(scheduler_name)

if __name__ == "__main__":
    # 演示用法
    config = OutputConfig()
    config.print_structure()
    
    # 创建所有目录
    config.create_all_directories()
    
    # 演示获取路径
    print("\n📋 Example Paths:")
    training_paths = config.get_training_paths("test_task")
    for key, path in training_paths.items():
        print(f"   {key}: {path}")
