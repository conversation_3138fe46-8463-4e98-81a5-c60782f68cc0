#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SeedVision v1 调度器模块

包含：
1. ResourceEstimator - 资源预估器
2. TaskScheduler - 任务调度器  
3. ProcessManager - 进程管理器
4. SchedulerManager - 统一调度管理器
"""

from .resource_estimator import ResourceEstimator
from .task_scheduler import TaskScheduler, TrainingTask, TaskStatus, TaskPriority
from .process_manager import ProcessManager, ProcessInfo, ProcessStatus

__all__ = [
    'ResourceEstimator',
    'TaskScheduler', 
    'TrainingTask',
    'TaskStatus',
    'TaskPriority',
    'ProcessManager',
    'ProcessInfo', 
    'ProcessStatus',
    'SchedulerManager'
]

class SchedulerManager:
    """
    统一调度管理器
    
    整合资源预估、任务调度和进程管理功能
    """
    
    def __init__(self, max_gpu_memory: float = 8.0, max_concurrent_tasks: int = 1, 
                 log_dir: str = "logs"):
        """
        初始化调度管理器
        
        参数:
            max_gpu_memory: 最大GPU显存限制 (GB)
            max_concurrent_tasks: 最大并发任务数
            log_dir: 日志目录
        """
        self.resource_estimator = ResourceEstimator()
        self.task_scheduler = TaskScheduler(max_gpu_memory, max_concurrent_tasks)
        self.process_manager = ProcessManager(log_dir, max_concurrent_tasks)
        
        # 集成调度器和进程管理器
        self._integrate_components()
    
    def _integrate_components(self):
        """集成各组件"""
        # 重写任务调度器的启动任务方法
        original_start_task = self.task_scheduler._start_task
        
        def integrated_start_task(task):
            """集成的启动任务方法"""
            try:
                # 启动进程
                process_id = self.process_manager.start_training_process(
                    task.task_id, 
                    task.config
                )
                
                if process_id:
                    task.process_id = process_id
                    task.status = TaskStatus.RUNNING
                    
                    # 设置进程状态变化回调
                    process_info = self.process_manager.get_process_status(process_id)
                    if process_info:
                        process_info.on_status_change = lambda pi: self._on_process_status_change(task, pi)
                    
                    if task.on_start:
                        task.on_start(task)
                else:
                    task.status = TaskStatus.FAILED
                    task.error_message = "Failed to start process"
                    if task.on_error:
                        task.on_error(task, Exception("Failed to start process"))
                        
            except Exception as e:
                task.status = TaskStatus.FAILED
                task.error_message = str(e)
                if task.on_error:
                    task.on_error(task, e)
        
        # 替换原方法
        self.task_scheduler._start_task = integrated_start_task
    
    def _on_process_status_change(self, task, process_info):
        """进程状态变化回调"""
        if process_info.status == ProcessStatus.STOPPED:
            task.status = TaskStatus.COMPLETED
            task.end_time = process_info.end_time
            if task.on_complete:
                task.on_complete(task)
                
        elif process_info.status in [ProcessStatus.FAILED, ProcessStatus.KILLED]:
            task.status = TaskStatus.FAILED
            task.end_time = process_info.end_time
            task.error_message = f"Process {process_info.status.value}"
            if task.on_error:
                task.on_error(task, Exception(task.error_message))
    
    def start(self):
        """启动调度管理器"""
        self.task_scheduler.start_scheduler()
        self.process_manager.start_monitoring()
    
    def stop(self):
        """停止调度管理器"""
        self.task_scheduler.stop_scheduler()
        self.process_manager.stop_monitoring()
    
    def submit_task(self, task: TrainingTask) -> str:
        """
        提交训练任务
        
        参数:
            task: 训练任务
            
        返回:
            任务ID
        """
        return self.task_scheduler.add_task(task)
    
    def cancel_task(self, task_id: str) -> bool:
        """
        取消任务
        
        参数:
            task_id: 任务ID
            
        返回:
            是否成功取消
        """
        # 先停止进程
        self.process_manager.stop_task(task_id)
        # 再从调度器移除
        return self.task_scheduler.remove_task(task_id)
    
    def get_status(self) -> dict:
        """
        获取整体状态
        
        返回:
            状态信息
        """
        return {
            'scheduler': self.task_scheduler.get_queue_status(),
            'processes': self.process_manager.get_system_info(),
            'resources': self.resource_estimator.get_system_resources()
        }
    
    def generate_report(self) -> dict:
        """
        生成完整报告
        
        返回:
            完整报告
        """
        return {
            'scheduler_report': self.task_scheduler.generate_schedule_report(),
            'process_report': self.process_manager.generate_process_report(),
            'task_history': self.task_scheduler.get_task_history()
        }
