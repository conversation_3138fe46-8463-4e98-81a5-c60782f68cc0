#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调度器调试测试 - 专门用于调试调度器问题

功能：
1. 简化的调度器测试
2. 详细的状态监控
3. 问题诊断和日志
"""

import os
import sys
import time
from datetime import datetime

# 添加项目根路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(project_root)

from scheduler import SchedulerManager, TrainingTask, TaskPriority

class Colors:
    RED = '\033[91m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    MAGENTA = '\033[95m'
    CYAN = '\033[96m'
    BOLD = '\033[1m'
    END = '\033[0m'

def create_test_task(task_id: str, name: str) -> TrainingTask:
    """创建测试任务"""
    # 简化的配置
    config = {
        'name': name,
        'model': {
            'embed_dim': 64,
            'depths': [2, 2, 6, 2],
            'drop_path_rate': 0.1
        },
        'resources': {
            'batch_size': 32,
            'estimated_memory': 0.5
        },
        'training': {
            'num_epochs': 1
        }
    }
    
    task = TrainingTask(
        task_id=task_id,
        name=name,
        config=config,
        priority=TaskPriority.NORMAL
    )
    
    # 添加回调函数
    task.on_test_start = lambda t: print(f"  🧪 {Colors.CYAN}测试开始{Colors.END}: {t.name}")
    task.on_test_complete = lambda t: print(f"  ✅ {Colors.GREEN}测试通过{Colors.END}: {t.name}")
    task.on_test_failed = lambda t, e: print(f"  ❌ {Colors.RED}测试失败{Colors.END}: {t.name} - {e}")
    task.on_start = lambda t: print(f"  🚀 {Colors.BLUE}训练开始{Colors.END}: {t.name}")
    task.on_complete = lambda t: print(f"  🎉 {Colors.GREEN}训练完成{Colors.END}: {t.name}")
    task.on_error = lambda t, e: print(f"  💥 {Colors.RED}训练失败{Colors.END}: {t.name} - {e}")
    
    return task

def debug_scheduler():
    """调试调度器"""
    print(f"{Colors.BOLD}🔧 调度器调试测试{Colors.END}")
    print("=" * 60)
    
    # 创建调度器
    scheduler = SchedulerManager(
        max_gpu_memory=4.0,
        max_concurrent_tasks=1,
        log_dir="logs/debug"
    )
    
    print(f"📋 创建调度器: 最大显存=4.0GB, 最大并发=1")
    
    # 启动调度器
    scheduler.start()
    print(f"🚀 调度器已启动")
    
    # 创建测试任务
    tasks = [
        create_test_task("debug_01", "test_task_1"),
        create_test_task("debug_02", "test_task_2"),
        create_test_task("debug_03", "test_task_3")
    ]
    
    # 提交任务
    print(f"\n📤 提交任务:")
    for task in tasks:
        task_id = scheduler.submit_task(task)
        print(f"  - {task.name} (ID: {task_id})")
    
    # 监控执行
    print(f"\n👀 监控执行状态:")
    start_time = time.time()
    last_status = None
    
    for i in range(60):  # 最多监控5分钟
        status = scheduler.get_status()
        queue_status = status['scheduler']
        
        current_status = (
            queue_status['pending_count'],
            queue_status['testing_count'], 
            queue_status['running_count'],
            queue_status['completed_count'],
            queue_status['failed_count'],
            queue_status['test_failed_count']
        )
        
        if current_status != last_status:
            elapsed = time.time() - start_time
            print(f"  ⏰ {datetime.now().strftime('%H:%M:%S')} (运行 {elapsed/60:.1f} 分钟)")
            print(f"  📊 队列状态: 等待={queue_status['pending_count']}, "
                  f"测试中={queue_status['testing_count']}, "
                  f"运行={queue_status['running_count']}, "
                  f"完成={queue_status['completed_count']}, "
                  f"失败={queue_status['failed_count']}, "
                  f"测试失败={queue_status['test_failed_count']}")
            
            last_status = current_status
        
        # 检查是否完成
        total_active = (queue_status['pending_count'] + 
                       queue_status['testing_count'] + 
                       queue_status['running_count'])
        
        if total_active == 0:
            print(f"  ✅ 所有任务已完成")
            break
        
        time.sleep(5)  # 每5秒检查一次
    
    # 生成报告
    print(f"\n📋 最终报告:")
    report = scheduler.generate_report()
    stats = report['scheduler_report']['statistics']
    
    print(f"  - 总提交: {stats['total_submitted']}")
    print(f"  - 总完成: {stats['total_completed']}")
    print(f"  - 总失败: {stats['total_failed']}")
    print(f"  - 成功率: {stats['success_rate_percent']:.1f}%")
    
    # 任务历史
    history = report['task_history']
    print(f"\n📚 任务历史:")
    for task_info in history:
        status_icon = {
            'completed': '✅',
            'failed': '❌',
            'test_failed': '🧪❌',
            'running': '🔄',
            'testing': '🧪',
            'pending': '⏳'
        }.get(task_info['status'], '❓')
        
        print(f"  {status_icon} {task_info['name']} ({task_info['status']})")
        if task_info['error_message']:
            print(f"    错误: {task_info['error_message']}")
    
    # 停止调度器
    scheduler.stop()
    print(f"\n🛑 调度器已停止")
    
    return stats['total_completed'] > 0 or stats['total_failed'] > 0

def main():
    """主函数"""
    print(f"{Colors.BOLD}🔧 SeedVision v1 - 调度器调试测试{Colors.END}")
    print("=" * 80)
    
    try:
        success = debug_scheduler()
        
        if success:
            print(f"\n{Colors.GREEN}✅ 调试测试完成 - 调度器正常工作{Colors.END}")
        else:
            print(f"\n{Colors.YELLOW}⚠️  调试测试完成 - 可能存在问题{Colors.END}")
            
    except KeyboardInterrupt:
        print(f"\n\n{Colors.YELLOW}⚠️  测试被用户中断{Colors.END}")
    except Exception as e:
        print(f"\n{Colors.RED}❌ 测试异常: {e}{Colors.END}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
