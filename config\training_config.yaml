dataset_sampling_strategies:
  original_balanced: 2400
  size_based:
    112x112: 4500
    224x224: 2000
    56x56: 6000
global_settings:
  max_gpu_memory: 8.0
  num_epochs: 50
  total_images: 190000
hyperparameters:
  adaptive:
    description: 自适应配置 - 自适应学习率
    gradient_clip: 2.0
    learning_rate: 0.001
    name: adaptive
    optimizer: RMSprop
    scheduler: ReduceLROnPlateau
    scheduler_params:
      factor: 0.2
      mode: min
      patience: 3
    weight_decay: 0.0001
  aggressive:
    description: 激进配置 - 较大学习率，快速收敛
    gradient_clip: 0.5
    learning_rate: 0.01
    name: aggressive
    optimizer: SGD
    scheduler: CosineAnnealingLR
    scheduler_params:
      T_max: 50
    weight_decay: 0.001
  conservative:
    description: 保守配置 - 小学习率，适合初始训练
    gradient_clip: 1.0
    learning_rate: 0.0001
    name: conservative
    optimizer: Adam
    scheduler: ReduceLROnPlateau
    scheduler_params:
      factor: 0.5
      mode: min
      patience: 5
    weight_decay: 0.0001
  fine_tune:
    description: 精细调优配置 - 用于微调
    gradient_clip: null
    learning_rate: 1.0e-05
    name: fine_tune
    optimizer: AdamW
    scheduler: CosineAnnealingWarmRestarts
    scheduler_params:
      T_0: 10
      T_mult: 2
    weight_decay: 1.0e-06
  stable:
    description: 稳定配置 - 稳定训练，避免震荡
    gradient_clip: 1.5
    learning_rate: 0.0005
    name: stable
    optimizer: Adam
    scheduler: ExponentialLR
    scheduler_params:
      gamma: 0.95
    weight_decay: 0.0001
  standard:
    description: 标准配置 - 平衡的参数
    gradient_clip: null
    learning_rate: 0.001
    name: standard
    optimizer: Adam
    scheduler: StepLR
    scheduler_params:
      gamma: 0.1
      step_size: 20
    weight_decay: 1.0e-05
  user_high_lr:
    description: 用户指定配置 - 高学习率0.01，Adam优化器
    gradient_clip: 1.0
    learning_rate: 0.01
    name: user_high_lr
    optimizer: Adam
    scheduler: ReduceLROnPlateau
    scheduler_params:
      factor: 0.5
      mode: min
      patience: 10
    weight_decay: 0.0001
  user_low_lr:
    description: 用户指定配置 - 低学习率0.0001，Adam优化器
    gradient_clip: 1.0
    learning_rate: 0.0001
    name: user_low_lr
    optimizer: Adam
    scheduler: CosineAnnealingLR
    scheduler_params:
      T_max: 100
    weight_decay: 0.0001
  user_mid_lr:
    description: 用户指定配置 - 中学习率0.001，Adam优化器
    gradient_clip: 1.0
    learning_rate: 0.001
    name: user_mid_lr
    optimizer: Adam
    scheduler: StepLR
    scheduler_params:
      gamma: 0.1
      step_size: 30
    weight_decay: 0.0001
  user_very_low_lr:
    description: 用户指定配置 - 极低学习率0.00001，Adam优化器
    gradient_clip: null
    learning_rate: 1.0e-05
    name: user_very_low_lr
    optimizer: Adam
    scheduler: ReduceLROnPlateau
    scheduler_params:
      factor: 0.5
      mode: min
      patience: 15
    weight_decay: 1.0e-05
sampling_strategies:
  balanced_sampling:
    description: 从每个original组中平衡采样，保持组间平衡
    parameters:
      max_groups: 45
      samples_per_group: 100
      total_samples: 4500
    strategy_type: balanced
  original_level_sampling:
    description: 基于original_image字段进行采样，确保数据分布平衡，支持Original级别评估
    parameters:
      cross_subset_sampling: true
      samples_per_original: 60
      target_originals: 40
      test_ratio: 0.1
      total_samples: 2400
      train_ratio: 0.8
      val_ratio: 0.1
    strategy_type: original_based
  random_sampling:
    description: 随机从数据集中采样指定数量的图片
    parameters:
      sample_size: 4500
      seed: 42
    strategy_type: random
  stratified_sampling:
    description: 按照标签分布进行分层采样，保持原始数据分布
    parameters:
      maintain_distribution: true
      min_samples_per_stratum: 10
      total_samples: 4500
    strategy_type: stratified
  temporal_sampling:
    description: 基于时间顺序的采样策略，适合时序数据分析
    parameters:
      time_window: recent
      total_samples: 3000
      window_size: 30
    strategy_type: temporal
training_configs:
  config_112x112_no_norm:
    description: 112x112 不带归一化 - 减小网络规模
    enable: false
    hyperparameter_config: fine_tune
    model:
      depths:
      - 2
      - 3
      - 12
      - 2
      drop_path_rate: 0.2
      embed_dim: 128
      layer_scale_init_value: 0
      mlp_ratio: 2.0
      n_div: 4
      patch_size: 2
      patch_stride: 2
    name: 112x112_no_norm
    resources:
      batch_size: 128
      estimated_memory: 0.8
    transform_config: 112x112_no_norm
  config_112x112_norm:
    description: 112x112 带归一化 - 减小网络规模
    enable: false
    hyperparameter_config: aggressive
    model:
      depths:
      - 2
      - 3
      - 12
      - 2
      drop_path_rate: 0.2
      embed_dim: 128
      layer_scale_init_value: 0
      mlp_ratio: 2.0
      n_div: 4
      patch_size: 2
      patch_stride: 2
    name: 112x112_norm
    resources:
      batch_size: 128
      estimated_memory: 0.8
    transform_config: 112x112_norm
  config_224x224_no_norm:
    description: 224x224 不带归一化 - 标准配置
    enable: false
    hyperparameter_config: standard
    model:
      depths:
      - 3
      - 4
      - 18
      - 3
      drop_path_rate: 0.3
      embed_dim: 192
      layer_scale_init_value: 0
      mlp_ratio: 2.0
      n_div: 4
      patch_size: 4
      patch_stride: 4
    name: 224x224_no_norm
    resources:
      batch_size: 64
      estimated_memory: 1.2
    transform_config: 224x224_no_norm
  config_224x224_norm:
    description: 224x224 带归一化 - 标准配置，传统随机采样
    enable: false
    hyperparameter_config: conservative
    model:
      depths:
      - 3
      - 4
      - 18
      - 3
      drop_path_rate: 0.3
      embed_dim: 192
      layer_scale_init_value: 0
      mlp_ratio: 2.0
      n_div: 4
      patch_size: 4
      patch_stride: 4
    name: 224x224_norm
    resources:
      batch_size: 64
      estimated_memory: 1.2
    transform_config: 224x224_norm
  config_56x56_no_norm:
    description: 56x56 不带归一化 - 最小网络规模
    enable: false
    hyperparameter_config: stable
    model:
      depths:
      - 2
      - 2
      - 8
      - 2
      drop_path_rate: 0.1
      embed_dim: 64
      layer_scale_init_value: 0
      mlp_ratio: 2.0
      n_div: 2
      patch_size: 1
      patch_stride: 1
    name: 56x56_no_norm
    resources:
      batch_size: 256
      estimated_memory: 0.5
    transform_config: 56x56_no_norm
  config_56x56_norm:
    description: 56x56 带归一化 - 最小网络规模
    enable: false
    hyperparameter_config: adaptive
    model:
      depths:
      - 2
      - 2
      - 8
      - 2
      drop_path_rate: 0.1
      embed_dim: 64
      layer_scale_init_value: 0
      mlp_ratio: 2.0
      n_div: 2
      patch_size: 1
      patch_stride: 1
    name: 56x56_norm
    resources:
      batch_size: 256
      estimated_memory: 0.5
    transform_config: 56x56_norm
  config_balanced_112x112_small:
    dataset_config:
      sampling_strategy: original_level_sampling
      strategy_parameters:
        samples_per_original: 30
        target_originals: 20
        total_samples: 600
    description: 小规模平衡采样 - 112x112 带归一化 - 20个original × 30条 = 600张图
    enable: false
    hyperparameter_config: aggressive
    model:
      depths:
      - 2
      - 3
      - 12
      - 2
      drop_path_rate: 0.2
      embed_dim: 128
      layer_scale_init_value: 0
      mlp_ratio: 2.0
      n_div: 4
      patch_size: 2
      patch_stride: 2
    name: balanced_112x112_small
    resources:
      batch_size: 20
      estimated_memory: 0.8
    training:
      num_epochs: 80
    transform_config: 112x112_norm
  config_balanced_224x224_large:
    dataset_config:
      sampling_strategy: original_level_sampling
      strategy_parameters:
        samples_per_original: 80
        target_originals: 60
        total_samples: 4800
    description: 大规模平衡采样 - 224x224 带归一化 - 60个original × 80条 = 4800张图
    enable: false
    hyperparameter_config: stable
    model:
      depths:
      - 3
      - 4
      - 18
      - 3
      drop_path_rate: 0.4
      embed_dim: 256
      layer_scale_init_value: 0
      mlp_ratio: 2.0
      n_div: 4
      patch_size: 4
      patch_stride: 4
    name: balanced_224x224_large
    resources:
      batch_size: 60
      estimated_memory: 2.0
    training:
      num_epochs: 120
    transform_config: 224x224_norm
  config_original_balanced_224x224:
    dataset_config:
      sampling_strategy: original_level_sampling
      strategy_parameters:
        cross_subset_sampling: true
        samples_per_original: 60
        target_originals: 40
        test_ratio: 0.1
        total_samples: 2400
        train_ratio: 0.8
        val_ratio: 0.1
    description: Original级别平衡采样 - 224x224 带归一化 - 基于original_image字段的平衡采样策略
    enable: false
    hyperparameter_config: conservative
    model:
      depths:
      - 3
      - 4
      - 18
      - 3
      drop_path_rate: 0.3
      embed_dim: 192
      layer_scale_init_value: 0
      mlp_ratio: 2.0
      n_div: 4
      patch_size: 4
      patch_stride: 4
    name: original_balanced_224x224
    resources:
      batch_size: 40
      estimated_memory: 1.2
    training:
      num_epochs: 100
    transform_config: 224x224_norm
  config_random_sampling_224x224:
    dataset_config:
      sampling_strategy: random_sampling
      strategy_parameters:
        sample_size: 4500
        seed: 42
    description: 随机采样策略 - 224x224 带归一化 - 随机采样4500张图片
    enable: false
    hyperparameter_config: standard
    model:
      depths:
      - 3
      - 4
      - 18
      - 3
      drop_path_rate: 0.3
      embed_dim: 192
      layer_scale_init_value: 0
      mlp_ratio: 2.0
      n_div: 4
      patch_size: 4
      patch_stride: 4
    name: random_sampling_224x224
    resources:
      batch_size: 64
      estimated_memory: 1.2
    training:
      num_epochs: 80
    transform_config: 224x224_norm
  config_stratified_sampling_224x224:
    dataset_config:
      sampling_strategy: stratified_sampling
      strategy_parameters:
        maintain_distribution: true
        min_samples_per_stratum: 10
        total_samples: 4500
    description: 分层采样策略 - 224x224 带归一化 - 按标签分布分层采样
    enable: false
    hyperparameter_config: adaptive
    model:
      depths:
      - 3
      - 4
      - 18
      - 3
      drop_path_rate: 0.3
      embed_dim: 192
      layer_scale_init_value: 0
      mlp_ratio: 2.0
      n_div: 4
      patch_size: 4
      patch_stride: 4
    name: stratified_sampling_224x224
    resources:
      batch_size: 64
      estimated_memory: 1.2
    training:
      num_epochs: 90
    transform_config: 224x224_norm
  user_112x112_no_norm_high_lr:
    dataset_config:
      sampling_strategy: original_level_sampling
      strategy_parameters:
        cross_subset_sampling: true
        samples_per_original: 60
        target_originals: 20
        test_ratio: 0.1
        total_samples: 1200
        train_ratio: 0.8
        val_ratio: 0.1
    description: 用户配置 - 112x112不带归一化，学习率0.01，20原图×60样本
    enable: true
    hyperparameter_config: user_high_lr
    model:
      depths:
      - 2
      - 3
      - 12
      - 2
      drop_path_rate: 0.2
      embed_dim: 128
      layer_scale_init_value: 0
      mlp_ratio: 2.0
      n_div: 4
      patch_size: 2
      patch_stride: 2
    name: user_112x112_no_norm_high_lr
    resources:
      batch_size: 48
      estimated_memory: 0.8
    training:
      num_epochs: 100
      save_params: true
    transform_config: 112x112_no_norm
  user_112x112_no_norm_low_lr:
    dataset_config:
      sampling_strategy: original_level_sampling
      strategy_parameters:
        cross_subset_sampling: true
        samples_per_original: 60
        target_originals: 20
        test_ratio: 0.1
        total_samples: 1200
        train_ratio: 0.8
        val_ratio: 0.1
    description: 用户配置 - 112x112不带归一化，学习率0.0001，20原图×60样本
    enable: true
    hyperparameter_config: user_low_lr
    model:
      depths:
      - 2
      - 3
      - 12
      - 2
      drop_path_rate: 0.2
      embed_dim: 128
      layer_scale_init_value: 0
      mlp_ratio: 2.0
      n_div: 4
      patch_size: 2
      patch_stride: 2
    name: user_112x112_no_norm_low_lr
    resources:
      batch_size: 48
      estimated_memory: 0.8
    training:
      num_epochs: 100
      save_params: true
    transform_config: 112x112_no_norm
  user_112x112_no_norm_mid_lr:
    dataset_config:
      sampling_strategy: original_level_sampling
      strategy_parameters:
        cross_subset_sampling: true
        samples_per_original: 60
        target_originals: 20
        test_ratio: 0.1
        total_samples: 1200
        train_ratio: 0.8
        val_ratio: 0.1
    description: 用户配置 - 112x112不带归一化，学习率0.001，20原图×60样本
    enable: true
    hyperparameter_config: user_mid_lr
    model:
      depths:
      - 2
      - 3
      - 12
      - 2
      drop_path_rate: 0.2
      embed_dim: 128
      layer_scale_init_value: 0
      mlp_ratio: 2.0
      n_div: 4
      patch_size: 2
      patch_stride: 2
    name: user_112x112_no_norm_mid_lr
    resources:
      batch_size: 48
      estimated_memory: 0.8
    training:
      num_epochs: 100
      save_params: true
    transform_config: 112x112_no_norm
  user_112x112_no_norm_very_low_lr:
    dataset_config:
      sampling_strategy: original_level_sampling
      strategy_parameters:
        cross_subset_sampling: true
        samples_per_original: 60
        target_originals: 20
        test_ratio: 0.1
        total_samples: 1200
        train_ratio: 0.8
        val_ratio: 0.1
    description: 用户配置 - 112x112不带归一化，学习率0.00001，20原图×60样本
    enable: true
    hyperparameter_config: user_very_low_lr
    model:
      depths:
      - 2
      - 3
      - 12
      - 2
      drop_path_rate: 0.2
      embed_dim: 128
      layer_scale_init_value: 0
      mlp_ratio: 2.0
      n_div: 4
      patch_size: 2
      patch_stride: 2
    name: user_112x112_no_norm_very_low_lr
    resources:
      batch_size: 48
      estimated_memory: 0.8
    training:
      num_epochs: 100
      save_params: true
    transform_config: 112x112_no_norm
  user_112x112_norm_high_lr:
    dataset_config:
      sampling_strategy: original_level_sampling
      strategy_parameters:
        cross_subset_sampling: true
        samples_per_original: 60
        target_originals: 20
        test_ratio: 0.1
        total_samples: 1200
        train_ratio: 0.8
        val_ratio: 0.1
    description: 用户配置 - 112x112带归一化，学习率0.01，20原图×60样本
    enable: true
    hyperparameter_config: user_high_lr
    model:
      depths:
      - 2
      - 3
      - 12
      - 2
      drop_path_rate: 0.2
      embed_dim: 128
      layer_scale_init_value: 0
      mlp_ratio: 2.0
      n_div: 4
      patch_size: 2
      patch_stride: 2
    name: user_112x112_norm_high_lr
    resources:
      batch_size: 48
      estimated_memory: 0.8
    training:
      num_epochs: 100
      save_params: true
    transform_config: 112x112_norm
  user_112x112_norm_low_lr:
    dataset_config:
      sampling_strategy: original_level_sampling
      strategy_parameters:
        cross_subset_sampling: true
        samples_per_original: 60
        target_originals: 20
        test_ratio: 0.1
        total_samples: 1200
        train_ratio: 0.8
        val_ratio: 0.1
    description: 用户配置 - 112x112带归一化，学习率0.0001，20原图×60样本
    enable: true
    hyperparameter_config: user_low_lr
    model:
      depths:
      - 2
      - 3
      - 12
      - 2
      drop_path_rate: 0.2
      embed_dim: 128
      layer_scale_init_value: 0
      mlp_ratio: 2.0
      n_div: 4
      patch_size: 2
      patch_stride: 2
    name: user_112x112_norm_low_lr
    resources:
      batch_size: 48
      estimated_memory: 0.8
    training:
      num_epochs: 100
      save_params: true
    transform_config: 112x112_norm
  user_112x112_norm_mid_lr:
    dataset_config:
      sampling_strategy: original_level_sampling
      strategy_parameters:
        cross_subset_sampling: true
        samples_per_original: 60
        target_originals: 20
        test_ratio: 0.1
        total_samples: 1200
        train_ratio: 0.8
        val_ratio: 0.1
    description: 用户配置 - 112x112带归一化，学习率0.001，20原图×60样本
    enable: true
    hyperparameter_config: user_mid_lr
    model:
      depths:
      - 2
      - 3
      - 12
      - 2
      drop_path_rate: 0.2
      embed_dim: 128
      layer_scale_init_value: 0
      mlp_ratio: 2.0
      n_div: 4
      patch_size: 2
      patch_stride: 2
    name: user_112x112_norm_mid_lr
    resources:
      batch_size: 48
      estimated_memory: 0.8
    training:
      num_epochs: 100
      save_params: true
    transform_config: 112x112_norm
  user_112x112_norm_very_low_lr:
    dataset_config:
      sampling_strategy: original_level_sampling
      strategy_parameters:
        cross_subset_sampling: true
        samples_per_original: 60
        target_originals: 20
        test_ratio: 0.1
        total_samples: 1200
        train_ratio: 0.8
        val_ratio: 0.1
    description: 用户配置 - 112x112带归一化，学习率0.00001，20原图×60样本
    enable: true
    hyperparameter_config: user_very_low_lr
    model:
      depths:
      - 2
      - 3
      - 12
      - 2
      drop_path_rate: 0.2
      embed_dim: 128
      layer_scale_init_value: 0
      mlp_ratio: 2.0
      n_div: 4
      patch_size: 2
      patch_stride: 2
    name: user_112x112_norm_very_low_lr
    resources:
      batch_size: 48
      estimated_memory: 0.8
    training:
      num_epochs: 100
      save_params: true
    transform_config: 112x112_norm
  user_224x224_no_norm_high_lr:
    dataset_config:
      sampling_strategy: original_level_sampling
      strategy_parameters:
        cross_subset_sampling: true
        samples_per_original: 60
        target_originals: 20
        test_ratio: 0.1
        total_samples: 1200
        train_ratio: 0.8
        val_ratio: 0.1
    description: 用户配置 - 224x224不带归一化，高学习率0.01，20原图×60样本
    enable: true
    hyperparameter_config: user_high_lr
    model:
      depths:
      - 3
      - 4
      - 18
      - 3
      drop_path_rate: 0.3
      embed_dim: 192
      layer_scale_init_value: 0
      mlp_ratio: 2.0
      n_div: 4
      patch_size: 4
      patch_stride: 4
    name: user_224x224_no_norm_high_lr
    resources:
      batch_size: 32
      estimated_memory: 1.2
    training:
      num_epochs: 100
      save_params: true
    transform_config: 224x224_no_norm
  user_224x224_no_norm_low_lr:
    dataset_config:
      sampling_strategy: original_level_sampling
      strategy_parameters:
        cross_subset_sampling: true
        samples_per_original: 60
        target_originals: 20
        test_ratio: 0.1
        total_samples: 1200
        train_ratio: 0.8
        val_ratio: 0.1
    description: 用户配置 - 224x224不带归一化，低学习率0.0001，20原图×60样本
    enable: true
    hyperparameter_config: user_low_lr
    model:
      depths:
      - 3
      - 4
      - 18
      - 3
      drop_path_rate: 0.3
      embed_dim: 192
      layer_scale_init_value: 0
      mlp_ratio: 2.0
      n_div: 4
      patch_size: 4
      patch_stride: 4
    name: user_224x224_no_norm_low_lr
    resources:
      batch_size: 32
      estimated_memory: 1.2
    training:
      num_epochs: 100
      save_params: true
    transform_config: 224x224_no_norm
  user_224x224_no_norm_mid_lr:
    dataset_config:
      sampling_strategy: original_level_sampling
      strategy_parameters:
        cross_subset_sampling: true
        samples_per_original: 60
        target_originals: 20
        test_ratio: 0.1
        total_samples: 1200
        train_ratio: 0.8
        val_ratio: 0.1
    description: 用户配置 - 224x224不带归一化，中学习率0.001，20原图×60样本
    enable: true
    hyperparameter_config: user_mid_lr
    model:
      depths:
      - 3
      - 4
      - 18
      - 3
      drop_path_rate: 0.3
      embed_dim: 192
      layer_scale_init_value: 0
      mlp_ratio: 2.0
      n_div: 4
      patch_size: 4
      patch_stride: 4
    name: user_224x224_no_norm_mid_lr
    resources:
      batch_size: 32
      estimated_memory: 1.2
    training:
      num_epochs: 100
      save_params: true
    transform_config: 224x224_no_norm
  user_224x224_no_norm_very_low_lr:
    dataset_config:
      sampling_strategy: original_level_sampling
      strategy_parameters:
        cross_subset_sampling: true
        samples_per_original: 60
        target_originals: 20
        test_ratio: 0.1
        total_samples: 1200
        train_ratio: 0.8
        val_ratio: 0.1
    description: 用户配置 - 224x224不带归一化，极低学习率0.00001，20原图×60样本
    enable: true
    hyperparameter_config: user_very_low_lr
    model:
      depths:
      - 3
      - 4
      - 18
      - 3
      drop_path_rate: 0.3
      embed_dim: 192
      layer_scale_init_value: 0
      mlp_ratio: 2.0
      n_div: 4
      patch_size: 4
      patch_stride: 4
    name: user_224x224_no_norm_very_low_lr
    resources:
      batch_size: 32
      estimated_memory: 1.2
    training:
      num_epochs: 100
      save_params: true
    transform_config: 224x224_no_norm
  user_224x224_norm_high_lr:
    dataset_config:
      sampling_strategy: original_level_sampling
      strategy_parameters:
        cross_subset_sampling: true
        samples_per_original: 60
        target_originals: 20
        test_ratio: 0.1
        total_samples: 1200
        train_ratio: 0.8
        val_ratio: 0.1
    description: 用户配置 - 224x224带归一化，高学习率0.01，20原图×60样本
    enable: true
    hyperparameter_config: user_high_lr
    model:
      depths:
      - 3
      - 4
      - 18
      - 3
      drop_path_rate: 0.3
      embed_dim: 192
      layer_scale_init_value: 0
      mlp_ratio: 2.0
      n_div: 4
      patch_size: 4
      patch_stride: 4
    name: user_224x224_norm_high_lr
    resources:
      batch_size: 32
      estimated_memory: 1.2
    training:
      num_epochs: 100
      save_params: true
    transform_config: 224x224_norm
  user_224x224_norm_low_lr:
    dataset_config:
      sampling_strategy: original_level_sampling
      strategy_parameters:
        cross_subset_sampling: true
        samples_per_original: 60
        target_originals: 20
        test_ratio: 0.1
        total_samples: 1200
        train_ratio: 0.8
        val_ratio: 0.1
    description: 用户配置 - 224x224带归一化，低学习率0.0001，20原图×60样本
    enable: true
    hyperparameter_config: user_low_lr
    model:
      depths:
      - 3
      - 4
      - 18
      - 3
      drop_path_rate: 0.3
      embed_dim: 192
      layer_scale_init_value: 0
      mlp_ratio: 2.0
      n_div: 4
      patch_size: 4
      patch_stride: 4
    name: user_224x224_norm_low_lr
    resources:
      batch_size: 32
      estimated_memory: 1.2
    training:
      num_epochs: 100
      save_params: true
    transform_config: 224x224_norm
  user_224x224_norm_mid_lr:
    dataset_config:
      sampling_strategy: original_level_sampling
      strategy_parameters:
        cross_subset_sampling: true
        samples_per_original: 60
        target_originals: 20
        test_ratio: 0.1
        total_samples: 1200
        train_ratio: 0.8
        val_ratio: 0.1
    description: 用户配置 - 224x224带归一化，中学习率0.001，20原图×60样本
    enable: true
    hyperparameter_config: user_mid_lr
    model:
      depths:
      - 3
      - 4
      - 18
      - 3
      drop_path_rate: 0.3
      embed_dim: 192
      layer_scale_init_value: 0
      mlp_ratio: 2.0
      n_div: 4
      patch_size: 4
      patch_stride: 4
    name: user_224x224_norm_mid_lr
    resources:
      batch_size: 32
      estimated_memory: 1.2
    training:
      num_epochs: 100
      save_params: true
    transform_config: 224x224_norm
  user_224x224_norm_very_low_lr:
    dataset_config:
      sampling_strategy: original_level_sampling
      strategy_parameters:
        cross_subset_sampling: true
        samples_per_original: 60
        target_originals: 20
        test_ratio: 0.1
        total_samples: 1200
        train_ratio: 0.8
        val_ratio: 0.1
    description: 用户配置 - 224x224带归一化，极低学习率0.00001，20原图×60样本
    enable: true
    hyperparameter_config: user_very_low_lr
    model:
      depths:
      - 3
      - 4
      - 18
      - 3
      drop_path_rate: 0.3
      embed_dim: 192
      layer_scale_init_value: 0
      mlp_ratio: 2.0
      n_div: 4
      patch_size: 4
      patch_stride: 4
    name: user_224x224_norm_very_low_lr
    resources:
      batch_size: 32
      estimated_memory: 1.2
    training:
      num_epochs: 100
      save_params: true
    transform_config: 224x224_norm
  user_56x56_no_norm_high_lr:
    dataset_config:
      sampling_strategy: original_level_sampling
      strategy_parameters:
        cross_subset_sampling: true
        samples_per_original: 60
        target_originals: 20
        test_ratio: 0.1
        total_samples: 1200
        train_ratio: 0.8
        val_ratio: 0.1
    description: 用户配置 - 56x56不带归一化，学习率0.01，20原图×60样本
    enable: true
    hyperparameter_config: user_high_lr
    model:
      depths:
      - 2
      - 2
      - 6
      - 2
      drop_path_rate: 0.2
      embed_dim: 64
      layer_scale_init_value: 0
      mlp_ratio: 2.0
      n_div: 4
      patch_size: 1
      patch_stride: 1
    name: user_56x56_no_norm_high_lr
    resources:
      batch_size: 80
      estimated_memory: 0.4
    training:
      num_epochs: 100
      save_params: true
    transform_config: 56x56_no_norm
  user_56x56_no_norm_low_lr:
    dataset_config:
      sampling_strategy: original_level_sampling
      strategy_parameters:
        cross_subset_sampling: true
        samples_per_original: 60
        target_originals: 20
        test_ratio: 0.1
        total_samples: 1200
        train_ratio: 0.8
        val_ratio: 0.1
    description: 用户配置 - 56x56不带归一化，学习率0.0001，20原图×60样本
    enable: true
    hyperparameter_config: user_low_lr
    model:
      depths:
      - 2
      - 2
      - 6
      - 2
      drop_path_rate: 0.2
      embed_dim: 64
      layer_scale_init_value: 0
      mlp_ratio: 2.0
      n_div: 4
      patch_size: 1
      patch_stride: 1
    name: user_56x56_no_norm_low_lr
    resources:
      batch_size: 80
      estimated_memory: 0.4
    training:
      num_epochs: 100
      save_params: true
    transform_config: 56x56_no_norm
  user_56x56_no_norm_mid_lr:
    dataset_config:
      sampling_strategy: original_level_sampling
      strategy_parameters:
        cross_subset_sampling: true
        samples_per_original: 60
        target_originals: 20
        test_ratio: 0.1
        total_samples: 1200
        train_ratio: 0.8
        val_ratio: 0.1
    description: 用户配置 - 56x56不带归一化，学习率0.001，20原图×60样本
    enable: true
    hyperparameter_config: user_mid_lr
    model:
      depths:
      - 2
      - 2
      - 6
      - 2
      drop_path_rate: 0.2
      embed_dim: 64
      layer_scale_init_value: 0
      mlp_ratio: 2.0
      n_div: 4
      patch_size: 1
      patch_stride: 1
    name: user_56x56_no_norm_mid_lr
    resources:
      batch_size: 80
      estimated_memory: 0.4
    training:
      num_epochs: 100
      save_params: true
    transform_config: 56x56_no_norm
  user_56x56_no_norm_very_low_lr:
    dataset_config:
      sampling_strategy: original_level_sampling
      strategy_parameters:
        cross_subset_sampling: true
        samples_per_original: 60
        target_originals: 20
        test_ratio: 0.1
        total_samples: 1200
        train_ratio: 0.8
        val_ratio: 0.1
    description: 用户配置 - 56x56不带归一化，学习率0.00001，20原图×60样本
    enable: true
    hyperparameter_config: user_very_low_lr
    model:
      depths:
      - 2
      - 2
      - 6
      - 2
      drop_path_rate: 0.2
      embed_dim: 64
      layer_scale_init_value: 0
      mlp_ratio: 2.0
      n_div: 4
      patch_size: 1
      patch_stride: 1
    name: user_56x56_no_norm_very_low_lr
    resources:
      batch_size: 80
      estimated_memory: 0.4
    training:
      num_epochs: 100
      save_params: true
    transform_config: 56x56_no_norm
  user_56x56_norm_high_lr:
    dataset_config:
      sampling_strategy: original_level_sampling
      strategy_parameters:
        cross_subset_sampling: true
        samples_per_original: 60
        target_originals: 20
        test_ratio: 0.1
        total_samples: 1200
        train_ratio: 0.8
        val_ratio: 0.1
    description: 用户配置 - 56x56带归一化，学习率0.01，20原图×60样本
    enable: true
    hyperparameter_config: user_high_lr
    model:
      depths:
      - 2
      - 2
      - 6
      - 2
      drop_path_rate: 0.2
      embed_dim: 64
      layer_scale_init_value: 0
      mlp_ratio: 2.0
      n_div: 4
      patch_size: 1
      patch_stride: 1
    name: user_56x56_norm_high_lr
    resources:
      batch_size: 80
      estimated_memory: 0.4
    training:
      num_epochs: 100
      save_params: true
    transform_config: 56x56_norm
  user_56x56_norm_low_lr:
    dataset_config:
      sampling_strategy: original_level_sampling
      strategy_parameters:
        cross_subset_sampling: true
        samples_per_original: 60
        target_originals: 20
        test_ratio: 0.1
        total_samples: 1200
        train_ratio: 0.8
        val_ratio: 0.1
    description: 用户配置 - 56x56带归一化，学习率0.0001，20原图×60样本
    enable: true
    hyperparameter_config: user_low_lr
    model:
      depths:
      - 2
      - 2
      - 6
      - 2
      drop_path_rate: 0.2
      embed_dim: 64
      layer_scale_init_value: 0
      mlp_ratio: 2.0
      n_div: 4
      patch_size: 1
      patch_stride: 1
    name: user_56x56_norm_low_lr
    resources:
      batch_size: 80
      estimated_memory: 0.4
    training:
      num_epochs: 100
      save_params: true
    transform_config: 56x56_norm
  user_56x56_norm_mid_lr:
    dataset_config:
      sampling_strategy: original_level_sampling
      strategy_parameters:
        cross_subset_sampling: true
        samples_per_original: 60
        target_originals: 20
        test_ratio: 0.1
        total_samples: 1200
        train_ratio: 0.8
        val_ratio: 0.1
    description: 用户配置 - 56x56带归一化，学习率0.001，20原图×60样本
    enable: true
    hyperparameter_config: user_mid_lr
    model:
      depths:
      - 2
      - 2
      - 6
      - 2
      drop_path_rate: 0.2
      embed_dim: 64
      layer_scale_init_value: 0
      mlp_ratio: 2.0
      n_div: 4
      patch_size: 1
      patch_stride: 1
    name: user_56x56_norm_mid_lr
    resources:
      batch_size: 80
      estimated_memory: 0.4
    training:
      num_epochs: 100
      save_params: true
    transform_config: 56x56_norm
  user_56x56_norm_very_low_lr:
    dataset_config:
      sampling_strategy: original_level_sampling
      strategy_parameters:
        cross_subset_sampling: true
        samples_per_original: 60
        target_originals: 20
        test_ratio: 0.1
        total_samples: 1200
        train_ratio: 0.8
        val_ratio: 0.1
    description: 用户配置 - 56x56带归一化，学习率0.00001，20原图×60样本
    enable: true
    hyperparameter_config: user_very_low_lr
    model:
      depths:
      - 2
      - 2
      - 6
      - 2
      drop_path_rate: 0.2
      embed_dim: 64
      layer_scale_init_value: 0
      mlp_ratio: 2.0
      n_div: 4
      patch_size: 1
      patch_stride: 1
    name: user_56x56_norm_very_low_lr
    resources:
      batch_size: 80
      estimated_memory: 0.4
    training:
      num_epochs: 100
      save_params: true
    transform_config: 56x56_norm
  user_80x80_no_norm_high_lr:
    dataset_config:
      sampling_strategy: original_level_sampling
      strategy_parameters:
        cross_subset_sampling: true
        samples_per_original: 60
        target_originals: 20
        test_ratio: 0.1
        total_samples: 1200
        train_ratio: 0.8
        val_ratio: 0.1
    description: 用户配置 - 80x80不带归一化，学习率0.01，20原图×60样本
    enable: true
    hyperparameter_config: user_high_lr
    model:
      depths:
      - 2
      - 3
      - 8
      - 2
      drop_path_rate: 0.2
      embed_dim: 96
      layer_scale_init_value: 0
      mlp_ratio: 2.0
      n_div: 4
      patch_size: 2
      patch_stride: 2
    name: user_80x80_no_norm_high_lr
    resources:
      batch_size: 64
      estimated_memory: 0.6
    training:
      num_epochs: 100
      save_params: true
    transform_config: 80x80_no_norm
  user_80x80_no_norm_low_lr:
    dataset_config:
      sampling_strategy: original_level_sampling
      strategy_parameters:
        cross_subset_sampling: true
        samples_per_original: 60
        target_originals: 20
        test_ratio: 0.1
        total_samples: 1200
        train_ratio: 0.8
        val_ratio: 0.1
    description: 用户配置 - 80x80不带归一化，学习率0.0001，20原图×60样本
    enable: true
    hyperparameter_config: user_low_lr
    model:
      depths:
      - 2
      - 3
      - 8
      - 2
      drop_path_rate: 0.2
      embed_dim: 96
      layer_scale_init_value: 0
      mlp_ratio: 2.0
      n_div: 4
      patch_size: 2
      patch_stride: 2
    name: user_80x80_no_norm_low_lr
    resources:
      batch_size: 64
      estimated_memory: 0.6
    training:
      num_epochs: 100
      save_params: true
    transform_config: 80x80_no_norm
  user_80x80_no_norm_mid_lr:
    dataset_config:
      sampling_strategy: original_level_sampling
      strategy_parameters:
        cross_subset_sampling: true
        samples_per_original: 60
        target_originals: 20
        test_ratio: 0.1
        total_samples: 1200
        train_ratio: 0.8
        val_ratio: 0.1
    description: 用户配置 - 80x80不带归一化，学习率0.001，20原图×60样本
    enable: true
    hyperparameter_config: user_mid_lr
    model:
      depths:
      - 2
      - 3
      - 8
      - 2
      drop_path_rate: 0.2
      embed_dim: 96
      layer_scale_init_value: 0
      mlp_ratio: 2.0
      n_div: 4
      patch_size: 2
      patch_stride: 2
    name: user_80x80_no_norm_mid_lr
    resources:
      batch_size: 64
      estimated_memory: 0.6
    training:
      num_epochs: 100
      save_params: true
    transform_config: 80x80_no_norm
  user_80x80_no_norm_very_low_lr:
    dataset_config:
      sampling_strategy: original_level_sampling
      strategy_parameters:
        cross_subset_sampling: true
        samples_per_original: 60
        target_originals: 20
        test_ratio: 0.1
        total_samples: 1200
        train_ratio: 0.8
        val_ratio: 0.1
    description: 用户配置 - 80x80不带归一化，学习率0.00001，20原图×60样本
    enable: true
    hyperparameter_config: user_very_low_lr
    model:
      depths:
      - 2
      - 3
      - 8
      - 2
      drop_path_rate: 0.2
      embed_dim: 96
      layer_scale_init_value: 0
      mlp_ratio: 2.0
      n_div: 4
      patch_size: 2
      patch_stride: 2
    name: user_80x80_no_norm_very_low_lr
    resources:
      batch_size: 64
      estimated_memory: 0.6
    training:
      num_epochs: 100
      save_params: true
    transform_config: 80x80_no_norm
  user_80x80_norm_high_lr:
    dataset_config:
      sampling_strategy: original_level_sampling
      strategy_parameters:
        cross_subset_sampling: true
        samples_per_original: 60
        target_originals: 20
        test_ratio: 0.1
        total_samples: 1200
        train_ratio: 0.8
        val_ratio: 0.1
    description: 用户配置 - 80x80带归一化，学习率0.01，20原图×60样本
    enable: true
    hyperparameter_config: user_high_lr
    model:
      depths:
      - 2
      - 3
      - 8
      - 2
      drop_path_rate: 0.2
      embed_dim: 96
      layer_scale_init_value: 0
      mlp_ratio: 2.0
      n_div: 4
      patch_size: 2
      patch_stride: 2
    name: user_80x80_norm_high_lr
    resources:
      batch_size: 64
      estimated_memory: 0.6
    training:
      num_epochs: 100
      save_params: true
    transform_config: 80x80_norm
  user_80x80_norm_low_lr:
    dataset_config:
      sampling_strategy: original_level_sampling
      strategy_parameters:
        cross_subset_sampling: true
        samples_per_original: 60
        target_originals: 20
        test_ratio: 0.1
        total_samples: 1200
        train_ratio: 0.8
        val_ratio: 0.1
    description: 用户配置 - 80x80带归一化，学习率0.0001，20原图×60样本
    enable: true
    hyperparameter_config: user_low_lr
    model:
      depths:
      - 2
      - 3
      - 8
      - 2
      drop_path_rate: 0.2
      embed_dim: 96
      layer_scale_init_value: 0
      mlp_ratio: 2.0
      n_div: 4
      patch_size: 2
      patch_stride: 2
    name: user_80x80_norm_low_lr
    resources:
      batch_size: 64
      estimated_memory: 0.6
    training:
      num_epochs: 100
      save_params: true
    transform_config: 80x80_norm
  user_80x80_norm_mid_lr:
    dataset_config:
      sampling_strategy: original_level_sampling
      strategy_parameters:
        cross_subset_sampling: true
        samples_per_original: 60
        target_originals: 20
        test_ratio: 0.1
        total_samples: 1200
        train_ratio: 0.8
        val_ratio: 0.1
    description: 用户配置 - 80x80带归一化，学习率0.001，20原图×60样本
    enable: true
    hyperparameter_config: user_mid_lr
    model:
      depths:
      - 2
      - 3
      - 8
      - 2
      drop_path_rate: 0.2
      embed_dim: 96
      layer_scale_init_value: 0
      mlp_ratio: 2.0
      n_div: 4
      patch_size: 2
      patch_stride: 2
    name: user_80x80_norm_mid_lr
    resources:
      batch_size: 64
      estimated_memory: 0.6
    training:
      num_epochs: 100
      save_params: true
    transform_config: 80x80_norm
  user_80x80_norm_very_low_lr:
    dataset_config:
      sampling_strategy: original_level_sampling
      strategy_parameters:
        cross_subset_sampling: true
        samples_per_original: 60
        target_originals: 20
        test_ratio: 0.1
        total_samples: 1200
        train_ratio: 0.8
        val_ratio: 0.1
    description: 用户配置 - 80x80带归一化，学习率0.00001，20原图×60样本
    enable: true
    hyperparameter_config: user_very_low_lr
    model:
      depths:
      - 2
      - 3
      - 8
      - 2
      drop_path_rate: 0.2
      embed_dim: 96
      layer_scale_init_value: 0
      mlp_ratio: 2.0
      n_div: 4
      patch_size: 2
      patch_stride: 2
    name: user_80x80_norm_very_low_lr
    resources:
      batch_size: 64
      estimated_memory: 0.6
    training:
      num_epochs: 100
      save_params: true
    transform_config: 80x80_norm
transforms:
  112x112_no_norm:
    description: 112x112 不带归一化 - 用于对比实验
    input_size:
    - 112
    - 112
    normalize: false
  112x112_norm:
    description: 112x112 带归一化 - 平衡性能和计算效率
    input_size:
    - 112
    - 112
    mean:
    - 0.485
    - 0.456
    - 0.406
    normalize: true
    std:
    - 0.229
    - 0.224
    - 0.225
  224x224_no_norm:
    description: 224x224 不带归一化 - 用于对比实验
    input_size:
    - 224
    - 224
    normalize: false
  224x224_norm:
    description: 224x224 带归一化 - 标准配置，适合大多数情况
    input_size:
    - 224
    - 224
    mean:
    - 0.485
    - 0.456
    - 0.406
    normalize: true
    std:
    - 0.229
    - 0.224
    - 0.225
  56x56_no_norm:
    description: 56x56 不带归一化 - 用于对比实验
    input_size:
    - 56
    - 56
    normalize: false
  56x56_norm:
    description: 56x56 带归一化 - 快速训练和原型验证
    input_size:
    - 56
    - 56
    mean:
    - 0.485
    - 0.456
    - 0.406
    normalize: true
    std:
    - 0.229
    - 0.224
    - 0.225
  80x80_no_norm:
    description: 80x80 不带归一化 - 用于对比实验
    input_size:
    - 80
    - 80
    normalize: false
  80x80_norm:
    description: 80x80 带归一化 - 介于56x56和112x112之间的分辨率
    input_size:
    - 80
    - 80
    mean:
    - 0.485
    - 0.456
    - 0.406
    normalize: true
    std:
    - 0.229
    - 0.224
    - 0.225
