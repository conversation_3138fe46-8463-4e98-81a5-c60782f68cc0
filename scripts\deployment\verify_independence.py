#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SeedVisionTrain 项目独立性验证脚本

检查项目是否完全独立，不依赖外部路径
"""

import os
import sys
import re
import importlib.util

def check_file_imports(file_path):
    """检查文件中的导入语句"""
    issues = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查硬编码路径
        hardcoded_patterns = [
            r"E:\\Proj\\pytorch-model-train",
            r"E:/Proj/pytorch-model-train",
            r"sys\.path\.append\(['\"][^'\"]*pytorch-model-train[^'\"]*['\"]\)",
        ]
        
        for pattern in hardcoded_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            if matches:
                issues.append(f"硬编码路径: {matches}")
        
        # 检查可疑的导入
        suspicious_imports = [
            r"from\s+utils\.",
            r"import\s+utils\.",
        ]
        
        lines = content.split('\n')
        for i, line in enumerate(lines, 1):
            for pattern in suspicious_imports:
                if re.search(pattern, line) and 'sys.path.append' not in lines[max(0, i-5):i]:
                    issues.append(f"第{i}行可能的外部导入: {line.strip()}")
        
        return issues
        
    except Exception as e:
        return [f"读取文件错误: {e}"]

def test_core_imports():
    """测试核心模块导入"""
    print("\n🧪 测试核心模块导入")
    print("-" * 40)
    
    test_imports = [
        ("utils.logger", "from utils.logger import logger"),
        ("utils.db_utils", "from utils.db_utils import database_connecter"),
        ("config.config_loader", "from config.config_loader import ConfigLoader"),
        ("tools.training.train", "from tools.training.train import train_model"),
        ("models.FasterNet", "from models.FasterNet import model"),
    ]
    
    success_count = 0
    
    for module_name, import_statement in test_imports:
        try:
            exec(import_statement)
            print(f"✅ {module_name}")
            success_count += 1
        except Exception as e:
            print(f"❌ {module_name}: {e}")
    
    print(f"\n导入测试: {success_count}/{len(test_imports)} 成功")
    return success_count == len(test_imports)

def check_required_files():
    """检查必需的文件是否存在"""
    print("\n📁 检查必需文件")
    print("-" * 40)
    
    required_files = [
        "main.py",
        "utils/__init__.py",
        "utils/logger.py",
        "utils/db_utils.py",
        "utils/path_manage.py",
        "config/config_loader.py",
        "config/training_config.yaml",
        "tools/training/train.py",
        "tools/config/config_mongo_tool.py",
        "models/FasterNet.py",
        "runners/training/main.py",
    ]
    
    missing_files = []
    
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path}")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"\n⚠️  缺失文件: {len(missing_files)} 个")
        return False
    else:
        print(f"\n✅ 所有必需文件都存在")
        return True

def scan_all_files():
    """扫描所有Python文件查找问题"""
    print("\n🔍 扫描所有Python文件")
    print("-" * 40)
    
    total_issues = 0
    
    for root, dirs, files in os.walk('.'):
        # 跳过__pycache__目录
        dirs[:] = [d for d in dirs if d != '__pycache__']
        
        for file in files:
            if file.endswith('.py'):
                file_path = os.path.join(root, file)
                issues = check_file_imports(file_path)
                
                if issues:
                    print(f"\n⚠️  {file_path}:")
                    for issue in issues:
                        print(f"   - {issue}")
                    total_issues += len(issues)
                else:
                    print(f"✅ {file_path}")
    
    return total_issues

def main():
    """主函数"""
    print("🔍 SeedVisionTrain 项目独立性验证")
    print("=" * 60)
    
    # 切换到项目根目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    
    # 添加当前目录到Python路径
    sys.path.insert(0, '.')
    
    # 检查必需文件
    files_ok = check_required_files()
    
    # 扫描所有文件
    total_issues = scan_all_files()
    
    # 测试核心导入
    imports_ok = test_core_imports()
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 验证结果总结")
    print("=" * 60)
    
    if files_ok:
        print("✅ 文件完整性: 通过")
    else:
        print("❌ 文件完整性: 失败")
    
    if total_issues == 0:
        print("✅ 路径独立性: 通过")
    else:
        print(f"❌ 路径独立性: 发现 {total_issues} 个问题")
    
    if imports_ok:
        print("✅ 模块导入: 通过")
    else:
        print("❌ 模块导入: 失败")
    
    # 最终判断
    if files_ok and total_issues == 0 and imports_ok:
        print("\n🎉 项目完全独立！可以安全使用")
        return True
    else:
        print("\n⚠️  项目还需要进一步修复")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
