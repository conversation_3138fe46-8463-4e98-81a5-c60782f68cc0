'''
FasterNet模型训练脚本
实现功能：
1. 数据加载与训练
2. 结果评估与可视化
3. 模型保存

评估指标：
- 决定系数（R2）：衡量预测值与真实值的拟合程度（越接近 1 越好）
- 均方根误差（RMSE）：反映预测误差的标准差（值越小越好）
- 平均绝对误差（MAE）：计算预测值与真实值误差的绝对值均值（值越小越好）
- 剩余预测偏差（RPD）：评估模型预测准确性（RPD>2.5 表示良好）
'''

import os
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import numpy as np
from tqdm import tqdm
import matplotlib.pyplot as plt
import random
import torchvision.transforms as transforms
from datetime import datetime
from collections import defaultdict
import GPUtil
from PIL import Image
import logging
import argparse
import json
import sys

# 添加项目根路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(project_root)
from utils.logger import logger

# 导入自定义模块
from tools.data.load_data import *
from tools.training.validate import calculate_r2, calculate_rmse, calculate_mae, calculate_rpd
from models.FasterNet import model as FasterNet_model
from tools.training.visualize import (
    plot_train_val_loss,
    plot_protein_regression,
    plot_oil_regression
)
from config.output_config import get_training_paths, get_testing_paths

def get_gpu_memory_info():
    """获取GPU显存信息"""
    try:
        gpus = GPUtil.getGPUs()
        if gpus:
            gpu = gpus[0]
            used_memory = gpu.memoryUsed
            total_memory = gpu.memoryTotal
            return used_memory, total_memory
        return 0, 0
    except:
        return 0, 0

def format_memory(memory_mb):
    """格式化显存显示"""
    if memory_mb >= 1024:
        return f"{memory_mb/1024:.1f}GB"
    else:
        return f"{memory_mb:.0f}MB"

def setup_training_logger(log_file_path=None, task_name="Training", test_mode=False):
    """
    设置训练日志记录器，使用简单的日志系统

    参数:
        log_file_path: 日志文件路径
        task_name: 任务名称
        test_mode: 是否为测试模式

    返回:
        logger: 配置好的日志记录器
    """
    # 创建logger
    logger_name = f"training_{task_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    logger = logging.getLogger(logger_name)
    logger.setLevel(logging.INFO)

    # 清除现有的handlers
    logger.handlers.clear()

    # 设置格式
    formatter = logging.Formatter(
        '%(asctime)s - %(levelname)s - %(message)s'
    )

    # 控制台输出
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)

    # 文件输出
    if log_file_path:
        # 确保日志目录存在
        os.makedirs(os.path.dirname(log_file_path), exist_ok=True)

        file_handler = logging.FileHandler(log_file_path, encoding='utf-8')
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)

    # 记录初始信息
    mode_str = "TEST MODE" if test_mode else "TRAINING MODE"
    logger.info(f"=== {mode_str} STARTED ===")
    logger.info(f"Task: {task_name}")
    if log_file_path:
        logger.info(f"Log file: {log_file_path}")

    return logger

# 设置随机种子，确保结果可复现
def set_seed(seed=123):
    """设置随机种子以确保结果可复现"""
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(seed)
        torch.cuda.manual_seed_all(seed)
        torch.backends.cudnn.deterministic = True
        torch.backends.cudnn.benchmark = False

# 自定义数据集类
class SeedDataset(Dataset):
    """种子图像数据集"""
    def __init__(self, data_list, transform=None):
        """
        初始化数据集

        参数:
            data_list: 包含(图像路径, 油含量, 蛋白质含量)元组的列表
            transform: 图像预处理转换
        """
        self.data_list = data_list
        self.transform = transform

        # 默认转换
        if self.transform is None:
            self.transform = transforms.Compose([
                transforms.Resize((224, 224)),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
            ])

    def __len__(self):
        return len(self.data_list)

    def __getitem__(self, idx):
        # 处理新的数据格式，支持字典和元组格式
        data_item = self.data_list[idx]
        original_name = 'unknown'  # 默认值

        # 处理字典格式的数据（跨子集采样返回的格式）
        if isinstance(data_item, dict):
            image_path = data_item['path']
            oil = float(data_item['oil'])
            protein = float(data_item['protein'])
            original_name = data_item.get('original_image', 'unknown')

            # 尝试打开图像
            try:
                image = Image.open(image_path)
            except Exception as e:
                logger.warning(f"  Failed to load image {image_path}: {e}")
                # 创建一个默认的黑色图像
                image = Image.new('RGB', (224, 224), color='black')

        # 处理元组格式的数据（传统格式）
        elif isinstance(data_item, (tuple, list)):
            if len(data_item) == 4:  # (image, oil, protein, original_image)
                image, oil, protein, original_name = data_item
            elif len(data_item) == 3:  # (image, oil, protein) - 兼容旧格式
                image, oil, protein = data_item
            else:
                raise ValueError(f"Unexpected tuple/list format: expected 3 or 4 elements, got {len(data_item)}")

        else:
            raise ValueError(f"Unexpected data format: expected dict or tuple/list, got {type(data_item)}")

        # 应用转换
        if self.transform:
            image = self.transform(image)

        # 创建标签张量 [油含量, 蛋白质含量]
        label = torch.tensor([oil, protein], dtype=torch.float32)

        return image, label, original_name



def train_epoch(model, optimizer, criterion, train_loader, device, hyperparam_config=None):
    """
    训练一个epoch

    参数:
        model: 模型
        optimizer: 优化器
        criterion: 损失函数
        train_loader: 训练数据加载器
        device: 设备(CPU/GPU)
        hyperparam_config: 超参数配置

    返回:
        epoch_loss: 平均损失
        metrics: 训练集评估指标
        all_preds: 所有预测值
        all_labels: 所有真实值
    """
    model.train()  # 设置模型为训练模式
    total_loss = 0.0
    all_preds = []
    all_labels = []

    # 获取梯度裁剪参数
    gradient_clip = None
    if hyperparam_config and 'gradient_clip' in hyperparam_config:
        gradient_clip = hyperparam_config['gradient_clip']

    for batch_data in tqdm(train_loader, desc="Training"):
        if len(batch_data) == 3:  # 新格式：(inputs, targets, original_names)
            inputs, targets, _ = batch_data  # 训练时不需要original_names
        else:  # 旧格式：(inputs, targets)
            inputs, targets = batch_data

        inputs, targets = inputs.to(device), targets.to(device)  # 将数据移动到设备上
        optimizer.zero_grad()  # 清零梯度
        outputs = model(inputs)  # 前向传播
        loss = criterion(outputs, targets)  # 计算损失
        loss.backward()  # 反向传播

        # 梯度裁剪
        if gradient_clip is not None:
            torch.nn.utils.clip_grad_norm_(model.parameters(), gradient_clip)

        optimizer.step()  # 更新参数
        total_loss += loss.item() * inputs.size(0)  # 累加损失

        # 收集预测和标签用于评估
        all_preds.append(outputs.detach().cpu())
        all_labels.append(targets.detach().cpu())

    # 计算平均损失
    epoch_loss = total_loss / len(train_loader.dataset)

    # 合并所有预测和标签
    all_preds = torch.cat(all_preds, dim=0)
    all_labels = torch.cat(all_labels, dim=0)

    # 计算评估指标
    metrics = {
        'oil': {
            'R2': calculate_r2(all_labels[:, 0], all_preds[:, 0]),
            'RMSE': calculate_rmse(all_labels[:, 0], all_preds[:, 0]),
            'MAE': calculate_mae(all_labels[:, 0], all_preds[:, 0]),
            'RPD': calculate_rpd(all_labels[:, 0], all_preds[:, 0])
        },
        'protein': {
            'R2': calculate_r2(all_labels[:, 1], all_preds[:, 1]),
            'RMSE': calculate_rmse(all_labels[:, 1], all_preds[:, 1]),
            'MAE': calculate_mae(all_labels[:, 1], all_preds[:, 1]),
            'RPD': calculate_rpd(all_labels[:, 1], all_preds[:, 1])
        }
    }

    return epoch_loss, metrics, all_preds.numpy(), all_labels.numpy()

def validate(model, val_loader, criterion, device, enable_original_eval=True, logger=None):
    """
    在验证集上评估模型，支持样本级别和Original级别评估

    参数:
        model: 模型
        val_loader: 验证数据加载器
        criterion: 损失函数
        device: 设备(CPU/GPU)
        enable_original_eval: 是否启用Original级别评估
        logger: 日志记录器

    返回:
        val_loss: 验证集平均损失
        metrics: 验证集评估指标（包含样本级别和Original级别）
        all_preds: 所有预测值
        all_labels: 所有真实值
    """
    # 如果没有提供logger，使用print作为备选
    if logger is None:
        class DummyLogger:
            def info(self, msg): print(msg)
            def warning(self, msg): print(f"Warning: {msg}")
            def error(self, msg): print(f"Error: {msg}")
        logger = DummyLogger()
    model.eval()  # 设置模型为评估模式
    total_loss = 0.0
    all_preds = []
    all_labels = []
    all_original_names = []
    total_samples = 0

    # 检查验证集是否为空
    if len(val_loader.dataset) == 0:
        logger.warning("  Warning: Validation dataset is empty!")
        # 返回默认值
        dummy_metrics = {
            'sample_level': {
                'oil': {'R2': 0.0, 'RMSE': 0.0, 'MAE': 0.0, 'RPD': 0.0},
                'protein': {'R2': 0.0, 'RMSE': 0.0, 'MAE': 0.0, 'RPD': 0.0}
            }
        }
        return 0.0, dummy_metrics, np.array([]), np.array([])

    with torch.no_grad():  # 不计算梯度
        for batch_data in tqdm(val_loader, desc="Validation"):
            if len(batch_data) == 3:  # 新格式：(inputs, targets, original_names)
                inputs, targets, original_names = batch_data
                all_original_names.extend(original_names)
            else:  # 旧格式：(inputs, targets)
                inputs, targets = batch_data
                # 如果没有original信息，使用索引作为标识
                batch_size = inputs.size(0)
                original_names = [f"sample_{total_samples + i}" for i in range(batch_size)]
                all_original_names.extend(original_names)

            inputs, targets = inputs.to(device), targets.to(device)  # 将数据移动到设备上
            outputs = model(inputs)  # 前向传播
            loss = criterion(outputs, targets)  # 计算损失

            batch_size = inputs.size(0)
            total_loss += loss.item() * batch_size  # 累加损失
            total_samples += batch_size

            # 收集预测和标签用于评估
            all_preds.append(outputs.cpu())
            all_labels.append(targets.cpu())

    # 计算平均损失
    if total_samples > 0:
        val_loss = total_loss / total_samples
    else:
        logger.warning("  Warning: No validation samples processed!")
        val_loss = 0.0

    # 如果没有预测数据，返回默认值
    if len(all_preds) == 0:
        dummy_metrics = {
            'sample_level': {
                'oil': {'R2': 0.0, 'RMSE': 0.0, 'MAE': 0.0, 'RPD': 0.0},
                'protein': {'R2': 0.0, 'RMSE': 0.0, 'MAE': 0.0, 'RPD': 0.0}
            }
        }
        return val_loss, dummy_metrics, np.array([]), np.array([])

    # 合并所有预测和标签
    all_preds = torch.cat(all_preds, dim=0)
    all_labels = torch.cat(all_labels, dim=0)

    # 计算样本级别评估指标
    sample_metrics = {
        'oil': {
            'R2': calculate_r2(all_labels[:, 0], all_preds[:, 0]),
            'RMSE': calculate_rmse(all_labels[:, 0], all_preds[:, 0]),
            'MAE': calculate_mae(all_labels[:, 0], all_preds[:, 0]),
            'RPD': calculate_rpd(all_labels[:, 0], all_preds[:, 0])
        },
        'protein': {
            'R2': calculate_r2(all_labels[:, 1], all_preds[:, 1]),
            'RMSE': calculate_rmse(all_labels[:, 1], all_preds[:, 1]),
            'MAE': calculate_mae(all_labels[:, 1], all_preds[:, 1]),
            'RPD': calculate_rpd(all_labels[:, 1], all_preds[:, 1])
        }
    }

    # 构建最终的metrics字典
    metrics = {'sample_level': sample_metrics}

    # 如果启用Original级别评估且有original信息
    unique_originals = set(all_original_names)
    logger.info(f"📊 Original-level evaluation check:")
    logger.info(f"  - Enable original eval: {enable_original_eval}")
    logger.info(f"  - Total samples: {len(all_original_names)}")
    logger.info(f"  - Unique originals: {len(unique_originals)}")
    logger.info(f"  - Sample original names: {list(unique_originals)[:5]}...")  # 显示前5个

    if enable_original_eval and len(unique_originals) > 1:
        try:
            from tools.training.validate import evaluate_original_level

            logger.info(f"  - Triggering original-level evaluation...")

            # 进行Original级别评估
            original_metrics, original_results = evaluate_original_level(
                all_preds.numpy(),
                all_labels.numpy(),
                all_original_names
            )

            metrics['original_level'] = original_metrics
            metrics['original_results'] = original_results

            logger.info(f"📊 Original-level evaluation completed:")
            logger.info(f"  - Total originals: {len(original_results)}")
            logger.info(f"  - Oil R²: Sample={sample_metrics['oil']['R2']:.4f}, Original={original_metrics['oil']['R2']:.4f}")
            logger.info(f"  - Protein R²: Sample={sample_metrics['protein']['R2']:.4f}, Original={original_metrics['protein']['R2']:.4f}")

        except Exception as e:
            logger.warning(f"  Warning: Original-level evaluation failed: {e}")
            logger.info("Continuing with sample-level evaluation only...")
            import traceback
            logger.warning(f"  Traceback: {traceback.format_exc()}")
    else:
        logger.info(f"  - Original-level evaluation NOT triggered")
        if not enable_original_eval:
            logger.info(f"    Reason: Original evaluation disabled")
        elif len(unique_originals) <= 1:
            logger.info(f"    Reason: Not enough unique originals ({len(unique_originals)} <= 1)")

    return val_loss, metrics, all_preds.numpy(), all_labels.numpy()

def save_training_params(result_dir, task_name, model_config=None, hyperparam_config=None,
                        transform_config=None, dataset_config=None, training_config=None,
                        num_epochs=100, batch_size=128, seed=123, timestamp=None):
    """
    保存训练参数到输出目录

    参数:
        result_dir: 结果保存目录
        task_name: 任务名称
        model_config: 模型配置
        hyperparam_config: 超参数配置
        transform_config: 数据变换配置
        dataset_config: 数据集配置
        training_config: 训练配置
        num_epochs: 训练轮数
        batch_size: 批次大小
        seed: 随机种子
        timestamp: 时间戳
    """
    if timestamp is None:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    # 创建参数保存目录
    params_dir = os.path.join(result_dir, "training_params")
    os.makedirs(params_dir, exist_ok=True)

    # 收集所有训练参数
    training_params = {
        "task_info": {
            "task_name": task_name,
            "timestamp": timestamp,
            "training_date": datetime.now().isoformat(),
            "num_epochs": num_epochs,
            "batch_size": batch_size,
            "seed": seed
        },
        "model_config": model_config or {},
        "hyperparameter_config": hyperparam_config or {},
        "transform_config": transform_config or {},
        "dataset_config": dataset_config or {},
        "training_config": training_config or {}
    }

    # 保存为JSON文件
    params_file = os.path.join(params_dir, f"training_params_{timestamp}.json")
    with open(params_file, 'w', encoding='utf-8') as f:
        json.dump(training_params, f, indent=2, ensure_ascii=False)

    # 保存为YAML文件（更易读）
    try:
        import yaml
        params_yaml_file = os.path.join(params_dir, f"training_params_{timestamp}.yaml")
        with open(params_yaml_file, 'w', encoding='utf-8') as f:
            yaml.dump(training_params, f, default_flow_style=False, allow_unicode=True, indent=2)
    except ImportError:
        pass  # 如果没有yaml库，跳过

    return params_file

def train_model(model_config=None, hyperparam_config=None, transform=None, result_dir=None, device=None, num_epochs=100, batch_size=128, seed=123, task_name="FasterNet", sample_size=4500, sampling_strategy_config=None, original_sampling_config=None, use_original_sampling=False, log_file_path=None, test_mode=False, save_params=False, transform_config=None, dataset_config=None, training_config=None):
    """
    训练模型函数，可以被外部调用

    参数:
        model_config: 模型配置参数字典
        hyperparam_config: 超参数配置字典
        transform: 数据转换
        result_dir: 结果保存目录
        device: 训练设备
        num_epochs: 训练轮数
        batch_size: 批次大小 (默认128，适应4500张图片的处理能力)
        seed: 随机种子
        task_name: 任务名称，用于显示
        sample_size: 数据采样大小，从190000张图片中采样
        sampling_strategy_config: 采样策略配置字典
        original_sampling_config: original级别采样配置（向后兼容）
        use_original_sampling: 是否使用original级别采样（向后兼容）
        log_file_path: 日志文件路径
        test_mode: 是否为测试模式

    返回:
        history: 训练历史记录
        model: 训练好的模型
    """
    # 设置日志记录器
    logger = setup_training_logger(log_file_path, task_name, test_mode)

    # 设置随机种子
    set_seed(seed)

    # 设置设备
    if device is None:
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    logger.info(f"Using device: {device}")

    # 使用标准化的输出路径配置
    if result_dir is None:
        # 根据是否为测试模式选择路径配置
        if test_mode:
            paths = get_testing_paths(task_name)
            result_dir = paths['result_dir']
            if log_file_path is None:
                log_file_path = paths['log_file']
        else:
            paths = get_training_paths(task_name)
            result_dir = paths['result_dir']
            if log_file_path is None:
                log_file_path = paths['log_file']
    else:
        os.makedirs(result_dir, exist_ok=True)

    logger.info(f"Results will be saved to: {result_dir}")
    if log_file_path:
        logger.info(f"Log file: {log_file_path}")

    # 保存训练参数（如果启用）
    if save_params:
        logger.info("Saving training parameters...")
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        params_file = save_training_params(
            result_dir=result_dir,
            task_name=task_name,
            model_config=model_config,
            hyperparam_config=hyperparam_config,
            transform_config=transform_config,
            dataset_config=dataset_config,
            training_config=training_config,
            num_epochs=num_epochs,
            batch_size=batch_size,
            seed=seed,
            timestamp=timestamp
        )
        logger.info(f"Training parameters saved to: {params_file}")

    # 加载数据
    logger.info("Loading data...")
    data = load_data()

    logger.info(f"Raw Data Statistics:")
    logger.info(f"  - Total data: {len(data)}")

    # 处理采样策略配置
    if sampling_strategy_config:
        strategy_type = sampling_strategy_config.get('strategy_type')
        strategy_name = sampling_strategy_config.get('strategy_name', 'unknown')
        parameters = sampling_strategy_config.get('parameters', {})

        logger.info(f"Using Sampling Strategy: {strategy_name} ({strategy_type})")

        # 根据策略类型选择处理逻辑
        if strategy_type == 'original_based':
            # 使用original级别采样
            use_original_sampling = True
            original_sampling_config = parameters
            logger.info(f"  - Target originals: {parameters.get('target_originals', 'N/A')}")
            logger.info(f"  - Samples per original: {parameters.get('samples_per_original', 'N/A')}")
            logger.info(f"  - Total samples: {parameters.get('total_samples', 'N/A')}")
        elif strategy_type == 'random':
            logger.info(f"  - Random sampling with seed: {parameters.get('seed', 'N/A')}")
            logger.info(f"  - Sample size: {parameters.get('sample_size', 'N/A')}")
            sample_size = parameters.get('sample_size', sample_size)
        elif strategy_type in ['balanced', 'stratified']:
            logger.info(f"  - Total samples: {parameters.get('total_samples', 'N/A')}")
            sample_size = parameters.get('total_samples', sample_size)
        elif strategy_type == 'temporal':
            logger.info(f"  - Time window: {parameters.get('time_window', 'N/A')}")
            logger.info(f"  - Total samples: {parameters.get('total_samples', 'N/A')}")
            sample_size = parameters.get('total_samples', sample_size)

    # 根据是否使用original级别采样选择不同的数据加载策略
    if use_original_sampling and original_sampling_config:
        # 检查是否启用跨子集采样
        cross_subset_sampling = original_sampling_config.get('cross_subset_sampling', False)

        if cross_subset_sampling:
            logger.info(f"🌐 Using Cross-Subset Original Sampling Strategy:")
            logger.info(f"  - This will ignore train/val/test boundaries")
            logger.info(f"  - Sampling from entire dataset for each original")

            # 导入跨子集采样函数
            from tools.data.load_data import fixed_sample_by_original_cross_subset

            # 使用跨子集采样
            train_data, val_data, test_data, train_original_mapping = fixed_sample_by_original_cross_subset(
                data, original_sampling_config=original_sampling_config
            )

            logger.info(f"📊 Cross-Subset Sampling Results:")
            logger.info(f"  - Training data: {len(train_data)}")
            logger.info(f"  - Validation data: {len(val_data)}")
            logger.info(f"  - Test data: {len(test_data)}")

            # 为了兼容后续代码，创建batch格式的数据
            train_batch = train_data
            val_batch = val_data

            # 创建简化的original mapping（兼容现有代码）
            val_original_mapping = {k: v['val'] for k, v in train_original_mapping.items()}

        else:
            # 使用传统的子集内采样
            train_type = 'train'
            val_type = 'val'
            train_data = get_subset(data, train_type)
            val_data = get_subset(data, val_type)

            logger.info(f"  - Training data: {len(train_data)}")
            logger.info(f"  - Validation data: {len(val_data)}")

            # 继续使用原有的采样逻辑
            logger.info(f"🎯 Using Traditional Original Sampling Strategy:")
            logger.info(f"  - Target originals: {original_sampling_config['target_originals']}")
            logger.info(f"  - Samples per original: {original_sampling_config['samples_per_original']}")
            logger.info(f"  - Total samples: {original_sampling_config['total_samples']}")

            # 使用固定采样加载训练数据
            try:
                train_batch_result = load_batch(
                    train_data,
                    use_fixed_sampling=True,
                    original_sampling_config=original_sampling_config
                )

                if len(train_batch_result) == 3:
                    train_batch, train_original_mapping, _ = train_batch_result  # train_batch_sampler_info不需要使用
                else:
                    train_batch, train_original_mapping = train_batch_result

                # 检查训练集是否为空
                if len(train_batch) == 0:
                    raise ValueError("Training batch is empty! Cannot proceed with training.")

            except Exception as e:
                logger.error(f" Error in training data loading: {e}")
                logger.info("This is a critical error. Please check your data and configuration.")
                raise

            # 验证集使用较少的original和样本，但确保有足够的数据
            # 根据验证集的实际情况调整参数（验证集每个original平均只有6.8个样本）
            val_target_originals = max(5, min(15, original_sampling_config['target_originals'] // 3))
            val_samples_per_original = max(5, min(10, original_sampling_config['samples_per_original'] // 6))  # 保守采样
            val_original_config = {
                'target_originals': val_target_originals,
                'samples_per_original': val_samples_per_original,
                'total_samples': val_target_originals * val_samples_per_original
            }

            logger.info(f"🎯 Validation Sampling Configuration:")
            logger.info(f"  - Target originals: {val_original_config['target_originals']}")
            logger.info(f"  - Samples per original: {val_original_config['samples_per_original']}")
            logger.info(f"  - Total samples: {val_original_config['total_samples']}")

            try:
                val_batch_result = load_batch(
                    val_data,
                    use_fixed_sampling=True,
                    original_sampling_config=val_original_config
                )

                if len(val_batch_result) == 3:
                    val_batch, val_original_mapping, _ = val_batch_result  # val_batch_sampler_info不需要使用
                else:
                    val_batch, val_original_mapping = val_batch_result

                logger.info(f"📊 Original-level Dataset Sizes:")
                logger.info(f"  - Training set: {len(train_batch)} images from {len(train_original_mapping)} originals")
                logger.info(f"  - Validation set: {len(val_batch)} images from {len(val_original_mapping)} originals")

                # 检查验证集是否为空
                if len(val_batch) == 0:
                    logger.warning("  Warning: Validation batch is empty! Using fallback strategy...")
                    # 使用传统采样作为备选方案
                    val_sample_size = min(len(val_data), 200)  # 至少200个样本
                    val_batch = load_batch(
                        val_data,
                        balanced_sampling=True,
                        target_size=val_sample_size,
                        samples_per_original=5
                    )
                    logger.info(f"  - Fallback validation set: {len(val_batch)} images")

            except Exception as e:
                logger.warning(f"  Error in validation data loading: {e}")
                logger.info("Using fallback validation strategy...")
                # 使用传统采样作为备选方案
                val_sample_size = min(len(val_data), 200)
                val_batch = load_batch(
                    val_data,
                    balanced_sampling=True,
                    target_size=val_sample_size,
                    samples_per_original=5
                )
                logger.info(f"  - Fallback validation set: {len(val_batch)} images")

        # 创建数据集和数据加载器（跨子集采样或传统采样都使用相同的逻辑）
        train_dataset = SeedDataset(train_batch, transform=transform)
        val_dataset = SeedDataset(val_batch, transform=transform)

        train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
        val_loader = DataLoader(val_dataset, batch_size=batch_size)

    else:
        # 使用传统的平衡采样策略
        logger.info(f"Using Traditional Balanced Sampling Strategy...")

        # 获取训练和验证数据
        train_data = get_subset(data, 'train')
        val_data = get_subset(data, 'val')

        logger.info(f"  - Training data: {len(train_data)}")
        logger.info(f"  - Validation data: {len(val_data)}")

        original_groups_train = defaultdict(list)
        for item in train_data:
            original_image = item.get('original_image', 'unknown')
            original_groups_train[original_image].append(item)

        actual_originals = len(original_groups_train)
        avg_samples_per_original = len(train_data) / actual_originals if actual_originals > 0 else 0

        # 计算合理的每个original采样数量
        if sample_size < len(train_data):
            samples_per_original = max(1, sample_size // actual_originals)
            if sample_size % actual_originals > 0:
                samples_per_original += 1
        else:
            samples_per_original = int(avg_samples_per_original) + 1

        logger.info(f"🎯 Balanced Sampling Configuration:")
        logger.info(f"  - Target training samples: {sample_size}")
        logger.info(f"  - Actual original images: {actual_originals}")
        logger.info(f"  - Avg samples per original in DB: {avg_samples_per_original:.1f}")
        logger.info(f"  - Max samples per original for sampling: {samples_per_original}")

        # 使用平衡采样加载训练数据（启用original信息保留）
        train_batch = load_batch(
            train_data,
            balanced_sampling=True,
            target_size=sample_size,
            samples_per_original=samples_per_original,
            use_fixed_sampling=False,  # 明确指定不使用固定采样，但要保留original信息
            preserve_original_info=True  # 新参数：保留original信息
        )

        # 验证集也使用平衡采样，但数量较少
        val_sample_size = min(len(val_data), sample_size // 4)

        # 分析验证集的original_image分布
        original_groups_val = defaultdict(list)
        for item in val_data:
            original_image = item.get('original_image', 'unknown')
            original_groups_val[original_image].append(item)

        actual_originals_val = len(original_groups_val)
        val_samples_per_original = max(1, val_sample_size // actual_originals_val) if actual_originals_val > 0 else 1

        logger.info(f"🎯 Validation Sampling Configuration:")
        logger.info(f"  - Target validation samples: {val_sample_size}")
        logger.info(f"  - Actual original images in val: {actual_originals_val}")
        logger.info(f"  - Max samples per original for val: {val_samples_per_original}")

        val_batch = load_batch(
            val_data,
            balanced_sampling=True,
            target_size=val_sample_size,
            samples_per_original=val_samples_per_original,
            use_fixed_sampling=False,  # 明确指定不使用固定采样，但要保留original信息
            preserve_original_info=True  # 新参数：保留original信息
        )

        logger.info(f"📊 Final Dataset Sizes:")
        logger.info(f"  - Training set: {len(train_batch)} images")
        logger.info(f"  - Validation set: {len(val_batch)} images")

        # 创建数据集和数据加载器
        train_dataset = SeedDataset(train_batch, transform=transform)
        val_dataset = SeedDataset(val_batch, transform=transform)

        train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
        val_loader = DataLoader(val_dataset, batch_size=batch_size)

    # 初始化模型
    if model_config is None:
        model = FasterNet_model(num_classes=2, device=device).to(device)
    else:
        model = FasterNet_model(
            num_classes=2,
            device=device,
            embed_dim=model_config.get('embed_dim', 192),
            depths=model_config.get('depths', (3, 4, 18, 3)),
            mlp_ratio=model_config.get('mlp_ratio', 2.0),
            n_div=model_config.get('n_div', 4),
            drop_path_rate=model_config.get('drop_path_rate', 0.3),
            layer_scale_init_value=model_config.get('layer_scale_init_value', 0)
        ).to(device)

    # 使用默认超参数配置如果没有提供
    if hyperparam_config is None:
        hyperparam_config = {
            'learning_rate': 0.0001,
            'weight_decay': 1e-4,
            'optimizer': 'Adam',
            'scheduler': 'ReduceLROnPlateau',
            'scheduler_params': {'mode': 'min', 'factor': 0.5, 'patience': 5},
            'gradient_clip': 1.0
        }

    logger.info(f"Using hyperparameters: {hyperparam_config}")

    # 定义损失函数
    criterion = nn.MSELoss()

    # 根据配置创建优化器
    optimizer_name = hyperparam_config['optimizer']
    lr = hyperparam_config['learning_rate']
    weight_decay = hyperparam_config['weight_decay']

    if optimizer_name == 'Adam':
        optimizer = optim.Adam(model.parameters(), lr=lr, weight_decay=weight_decay)
    elif optimizer_name == 'AdamW':
        optimizer = optim.AdamW(model.parameters(), lr=lr, weight_decay=weight_decay)
    elif optimizer_name == 'SGD':
        optimizer = optim.SGD(model.parameters(), lr=lr, weight_decay=weight_decay, momentum=0.9)
    elif optimizer_name == 'RMSprop':
        optimizer = optim.RMSprop(model.parameters(), lr=lr, weight_decay=weight_decay)
    else:
        optimizer = optim.Adam(model.parameters(), lr=lr, weight_decay=weight_decay)

    # 根据配置创建学习率调度器
    scheduler_name = hyperparam_config['scheduler']
    scheduler_params = hyperparam_config['scheduler_params']

    if scheduler_name == 'ReduceLROnPlateau':
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, **scheduler_params)
    elif scheduler_name == 'StepLR':
        scheduler = optim.lr_scheduler.StepLR(optimizer, **scheduler_params)
    elif scheduler_name == 'CosineAnnealingLR':
        scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, **scheduler_params)
    elif scheduler_name == 'CosineAnnealingWarmRestarts':
        scheduler = optim.lr_scheduler.CosineAnnealingWarmRestarts(optimizer, **scheduler_params)
    elif scheduler_name == 'ExponentialLR':
        scheduler = optim.lr_scheduler.ExponentialLR(optimizer, **scheduler_params)
    else:
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='min', factor=0.5, patience=5)

    # 训练参数
    best_val_loss = float('inf')

    # 记录训练过程
    train_losses = []
    val_losses = []
    train_metrics_history = []
    val_metrics_history = []

    # 训练循环
    logger.info(f"Starting training for {num_epochs} epochs...")
    for epoch in range(num_epochs):
        # 获取GPU显存信息
        used_memory, total_memory = get_gpu_memory_info()
        used_str = format_memory(used_memory)
        total_str = format_memory(total_memory)

        logger.info(f"\nEpoch {epoch+1}/{num_epochs} - Task: {task_name} - GPU: {used_str}/{total_str}")

        # 训练一个epoch
        train_loss, train_metrics, _, _ = train_epoch(
            model, optimizer, criterion, train_loader, device, hyperparam_config
        )

        # 验证（明确启用Original级别评估）
        val_loss, val_metrics, val_preds, val_labels = validate(
            model, val_loader, criterion, device, enable_original_eval=True, logger=logger
        )

        # 记录损失和指标
        train_losses.append(train_loss)
        val_losses.append(val_loss)
        train_metrics_history.append(train_metrics)
        val_metrics_history.append(val_metrics)

        # 获取训练后的GPU显存信息
        used_memory_after, _ = get_gpu_memory_info()
        used_after_str = format_memory(used_memory_after)

        # 打印当前epoch的结果
        logger.info(f"Training Loss: {train_loss:.4f}, Validation Loss: {val_loss:.4f} - GPU After: {used_after_str}/{total_str}")

        # 获取验证指标（支持新旧格式）
        if 'sample_level' in val_metrics:
            val_sample_metrics = val_metrics['sample_level']
            logger.info(f"📊 Sample-level R²:")
            logger.info(f"  Oil: Training={train_metrics['oil']['R2']:.4f}, Validation={val_sample_metrics['oil']['R2']:.4f}")
            logger.info(f"  Protein: Training={train_metrics['protein']['R2']:.4f}, Validation={val_sample_metrics['protein']['R2']:.4f}")

            # 如果有Original级别评估结果，也显示
            if 'original_level' in val_metrics:
                val_original_metrics = val_metrics['original_level']
                logger.info(f"🎯 Original-level R²:")
                logger.info(f"  Oil: {val_original_metrics['oil']['R2']:.4f}")
                logger.info(f"  Protein: {val_original_metrics['protein']['R2']:.4f}")
        else:
            # 向后兼容旧格式
            logger.info(f"Oil Content - Training R2: {train_metrics['oil']['R2']:.4f}, Validation R2: {val_metrics['oil']['R2']:.4f}")
            logger.info(f"Protein Content - Training R2: {train_metrics['protein']['R2']:.4f}, Validation R2: {val_metrics['protein']['R2']:.4f}")

        # 更新学习率调度器
        scheduler_name = hyperparam_config['scheduler']
        if scheduler_name == 'ReduceLROnPlateau':
            scheduler.step(val_loss)
        else:
            scheduler.step()

        # 保存最佳模型
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            model_save_path = os.path.join(result_dir, "best_model.pth")
            torch.save(model.state_dict(), model_save_path)
            logger.info(f"Best model saved to: {model_save_path}")

    # 保存最终模型
    final_model_path = os.path.join(result_dir, "final_model.pth")
    torch.save(model.state_dict(), final_model_path)
    logger.info(f"Final model saved to: {final_model_path}")

    # 绘制训练过程图表
    logger.info("\nGenerating visualization results...")

    # 使用标准化的可视化路径
    if test_mode:
        viz_dir = get_testing_paths(task_name)['viz_dir']
    else:
        viz_dir = get_training_paths(task_name)['viz_dir']

    # 绘制损失曲线
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    loss_plot_path = os.path.join(viz_dir, f"loss_curve_{timestamp}.png")
    plot_train_val_loss(train_losses, val_losses,
                        title=f"FasterNet Training and Validation Loss Curve - {task_name}",
                        save_path=loss_plot_path)

    # 绘制R2指标曲线
    # 提取每个epoch的R2值（支持新旧格式）
    train_oil_r2 = [metrics['oil']['R2'] for metrics in train_metrics_history]
    train_protein_r2 = [metrics['protein']['R2'] for metrics in train_metrics_history]

    # 处理验证指标（支持新旧格式）
    val_oil_r2 = []
    val_protein_r2 = []
    val_original_oil_r2 = []
    val_original_protein_r2 = []

    for metrics in val_metrics_history:
        if 'sample_level' in metrics:
            # 新格式
            val_oil_r2.append(metrics['sample_level']['oil']['R2'])
            val_protein_r2.append(metrics['sample_level']['protein']['R2'])

            # 如果有Original级别评估
            if 'original_level' in metrics:
                val_original_oil_r2.append(metrics['original_level']['oil']['R2'])
                val_original_protein_r2.append(metrics['original_level']['protein']['R2'])
        else:
            # 旧格式
            val_oil_r2.append(metrics['oil']['R2'])
            val_protein_r2.append(metrics['protein']['R2'])

    # 绘制油含量R2曲线
    plt.figure(figsize=(12, 6))
    plt.plot(range(1, num_epochs+1), train_oil_r2, 'b-', label='Training', linewidth=2)
    plt.plot(range(1, num_epochs+1), val_oil_r2, 'r-', label='Validation (Sample-level)', linewidth=2)

    # 如果有Original级别评估，也绘制
    if val_original_oil_r2:
        plt.plot(range(1, len(val_original_oil_r2)+1), val_original_oil_r2, 'g--',
                label='Validation (Original-level)', linewidth=2)

    plt.title(f'Oil Content Prediction R² Curve - {task_name}', fontsize=16)
    plt.xlabel('Epoch', fontsize=14)
    plt.ylabel('R²', fontsize=14)
    plt.legend(fontsize=12)
    plt.grid(True, linestyle='--', alpha=0.7)
    oil_r2_path = os.path.join(viz_dir, f"oil_r2_curve_{timestamp}.png")
    plt.savefig(oil_r2_path, dpi=300, bbox_inches='tight')
    plt.close()

    # 绘制蛋白质含量R2曲线
    plt.figure(figsize=(12, 6))
    plt.plot(range(1, num_epochs+1), train_protein_r2, 'b-', label='Training', linewidth=2)
    plt.plot(range(1, num_epochs+1), val_protein_r2, 'r-', label='Validation (Sample-level)', linewidth=2)

    # 如果有Original级别评估，也绘制
    if val_original_protein_r2:
        plt.plot(range(1, len(val_original_protein_r2)+1), val_original_protein_r2, 'g--',
                label='Validation (Original-level)', linewidth=2)

    plt.title(f'Protein Content Prediction R² Curve - {task_name}', fontsize=16)
    plt.xlabel('Epoch', fontsize=14)
    plt.ylabel('R²', fontsize=14)
    plt.legend(fontsize=12)
    plt.grid(True, linestyle='--', alpha=0.7)
    protein_r2_path = os.path.join(viz_dir, f"protein_r2_curve_{timestamp}.png")
    plt.savefig(protein_r2_path, dpi=300, bbox_inches='tight')
    plt.close()

    # 绘制油含量回归图
    oil_plot_path = os.path.join(viz_dir, f"oil_regression_{timestamp}.png")
    plot_oil_regression(
        val_labels[:, 0],
        val_preds[:, 0],
        title=f"FasterNet Oil Content Prediction Results - {task_name}",
        save_path=oil_plot_path
    )

    # 绘制蛋白质含量回归图
    protein_plot_path = os.path.join(viz_dir, f"protein_regression_{timestamp}.png")
    plot_protein_regression(
        val_labels[:, 1],
        val_preds[:, 1],
        title=f"FasterNet Protein Content Prediction Results - {task_name}",
        save_path=protein_plot_path
    )

    # 如果有Original级别评估结果，生成Original级别回归图
    if val_metrics_history and 'original_level' in val_metrics_history[-1]:
        try:
            from .visualize import plot_original_level_regression

            # 获取最后一次验证的Original级别结果
            last_val_metrics = val_metrics_history[-1]
            if 'original_results' in last_val_metrics:
                original_results = last_val_metrics['original_results']

                # 提取Original级别的真实值和预测值
                original_oil_true = []
                original_oil_pred = []
                original_protein_true = []
                original_protein_pred = []

                for original_name, result in original_results.items():
                    original_oil_true.append(result['label'][0])  # 油含量标签
                    original_oil_pred.append(result['mean_prediction'][0])  # 油含量预测均值
                    original_protein_true.append(result['label'][1])  # 蛋白质含量标签
                    original_protein_pred.append(result['mean_prediction'][1])  # 蛋白质含量预测均值

                # 绘制Original级别油含量回归图
                original_oil_plot_path = os.path.join(viz_dir, f"oil_regression_original_{timestamp}.png")
                plot_original_level_regression(
                    original_oil_true,
                    original_oil_pred,
                    component_name='Oil',
                    title=f"FasterNet Oil Content Prediction Results (Original-Level) - {task_name}",
                    save_path=original_oil_plot_path
                )

                # 绘制Original级别蛋白质含量回归图
                original_protein_plot_path = os.path.join(viz_dir, f"protein_regression_original_{timestamp}.png")
                plot_original_level_regression(
                    original_protein_true,
                    original_protein_pred,
                    component_name='Protein',
                    title=f"FasterNet Protein Content Prediction Results (Original-Level) - {task_name}",
                    save_path=original_protein_plot_path
                )

                logger.info(f"📊 Original-level regression plots generated:")
                logger.info(f"  - Oil: {original_oil_plot_path}")
                logger.info(f"  - Protein: {original_protein_plot_path}")
                logger.info(f"  - Total originals: {len(original_results)}")

        except Exception as e:
            logger.warning(f"Failed to generate original-level regression plots: {e}")

    # 保存训练历史记录
    history = {
        'train_losses': train_losses,
        'val_losses': val_losses,
        'train_metrics': train_metrics_history,
        'val_metrics': val_metrics_history
    }

    logger.info(f"Training completed! All results saved to: {result_dir}")
    return history, model

def main():
    """
    主函数，直接运行train.py时调用
    """
    # 直接调用训练模型函数
    train_model()

if __name__ == "__main__":
    main()