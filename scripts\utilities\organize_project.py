#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SeedVision v1 - 项目整理脚本

功能：
1. 清理临时文件和缓存
2. 整理日志文件
3. 检查项目结构
4. 生成项目统计报告

使用方法：
python scripts/utilities/organize_project.py [--clean] [--logs] [--report]
"""

import os
import sys
import shutil
import argparse
import json
from datetime import datetime
from pathlib import Path

# 添加项目根路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(project_root)

def clean_cache_files():
    """清理缓存文件"""
    print("🧹 清理缓存文件...")
    
    cache_patterns = [
        '**/__pycache__',
        '**/*.pyc',
        '**/*.pyo',
        '**/.pytest_cache',
        '**/node_modules',
        '**/.DS_Store',
        '**/Thumbs.db'
    ]
    
    cleaned_count = 0
    
    for pattern in cache_patterns:
        for path in Path('.').glob(pattern):
            try:
                if path.is_dir():
                    shutil.rmtree(path)
                    print(f"  删除目录: {path}")
                else:
                    path.unlink()
                    print(f"  删除文件: {path}")
                cleaned_count += 1
            except Exception as e:
                print(f"  ⚠️  无法删除 {path}: {e}")
    
    print(f"✅ 清理完成，共清理 {cleaned_count} 个项目")
    return cleaned_count

def organize_logs():
    """整理日志文件"""
    print("📝 整理日志文件...")
    
    log_dirs = ['logs', 'output/training/logs', 'output/testing/logs']
    organized_count = 0
    
    for log_dir in log_dirs:
        if not os.path.exists(log_dir):
            continue
            
        print(f"  处理目录: {log_dir}")
        
        # 按日期整理日志
        for log_file in Path(log_dir).glob('*.log'):
            try:
                # 获取文件修改时间
                mtime = datetime.fromtimestamp(log_file.stat().st_mtime)
                date_dir = log_dir + f"/archive/{mtime.strftime('%Y-%m')}"
                
                # 创建归档目录
                os.makedirs(date_dir, exist_ok=True)
                
                # 移动文件
                new_path = Path(date_dir) / log_file.name
                if not new_path.exists():
                    shutil.move(str(log_file), str(new_path))
                    print(f"    归档: {log_file.name} -> {date_dir}")
                    organized_count += 1
                    
            except Exception as e:
                print(f"    ⚠️  无法处理 {log_file}: {e}")
    
    print(f"✅ 日志整理完成，共整理 {organized_count} 个文件")
    return organized_count

def check_project_structure():
    """检查项目结构"""
    print("📁 检查项目结构...")
    
    expected_structure = {
        'directories': [
            'scripts/deployment',
            'scripts/maintenance', 
            'scripts/utilities',
            'config',
            'models',
            'tools/data',
            'tools/training',
            'tools/config',
            'tools/analysis',
            'scheduler',
            'tests/unit',
            'tests/integration',
            'tests/examples',
            'tests/runners',
            'runners/training',
            'runners/utilities',
            'utils',
            'output/training',
            'output/testing',
            'logs',
            'weights'
        ],
        'files': [
            'main.py',
            'main_scheduler.py',
            'quick_test.py',
            'README.md',
            'config/config_loader.py',
            'config/training_config.yaml',
            'models/FasterNet.py',
            'tools/training/train.py',
            'scheduler/resource_estimator.py',
            'utils/logger.py',
            'utils/db_utils.py'
        ]
    }
    
    missing_items = []
    
    # 检查目录
    for directory in expected_structure['directories']:
        if not os.path.exists(directory):
            missing_items.append(f"目录: {directory}")
            print(f"  ❌ 缺少目录: {directory}")
        else:
            print(f"  ✅ {directory}/")
    
    # 检查文件
    for file_path in expected_structure['files']:
        if not os.path.exists(file_path):
            missing_items.append(f"文件: {file_path}")
            print(f"  ❌ 缺少文件: {file_path}")
        else:
            print(f"  ✅ {file_path}")
    
    if missing_items:
        print(f"⚠️  发现 {len(missing_items)} 个缺失项目")
        return False
    else:
        print("✅ 项目结构完整")
        return True

def generate_project_report():
    """生成项目统计报告"""
    print("📊 生成项目统计报告...")
    
    report = {
        'timestamp': datetime.now().isoformat(),
        'project_name': 'SeedVision v1',
        'statistics': {}
    }
    
    # 统计文件数量
    file_stats = {}
    total_size = 0
    
    for root, dirs, files in os.walk('.'):
        # 跳过缓存目录
        dirs[:] = [d for d in dirs if d not in ['__pycache__', '.git', 'node_modules']]
        
        for file in files:
            file_path = os.path.join(root, file)
            ext = os.path.splitext(file)[1].lower()
            
            if ext not in file_stats:
                file_stats[ext] = {'count': 0, 'size': 0}
            
            try:
                size = os.path.getsize(file_path)
                file_stats[ext]['count'] += 1
                file_stats[ext]['size'] += size
                total_size += size
            except:
                pass
    
    report['statistics'] = {
        'total_files': sum(stats['count'] for stats in file_stats.values()),
        'total_size_mb': round(total_size / (1024 * 1024), 2),
        'file_types': file_stats,
        'python_files': file_stats.get('.py', {}).get('count', 0),
        'config_files': file_stats.get('.yaml', {}).get('count', 0) + file_stats.get('.yml', {}).get('count', 0),
        'documentation': file_stats.get('.md', {}).get('count', 0)
    }
    
    # 保存报告
    report_file = f"project_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    print(f"✅ 报告已生成: {report_file}")
    
    # 显示摘要
    print("\n📈 项目统计摘要:")
    print(f"  总文件数: {report['statistics']['total_files']}")
    print(f"  项目大小: {report['statistics']['total_size_mb']} MB")
    print(f"  Python文件: {report['statistics']['python_files']}")
    print(f"  配置文件: {report['statistics']['config_files']}")
    print(f"  文档文件: {report['statistics']['documentation']}")
    
    return report_file

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='SeedVision v1 项目整理工具')
    parser.add_argument('--clean', action='store_true', help='清理缓存文件')
    parser.add_argument('--logs', action='store_true', help='整理日志文件')
    parser.add_argument('--report', action='store_true', help='生成项目报告')
    parser.add_argument('--all', action='store_true', help='执行所有操作')
    
    args = parser.parse_args()
    
    # 如果没有指定参数，显示帮助
    if not any([args.clean, args.logs, args.report, args.all]):
        parser.print_help()
        return
    
    print("🔧 SeedVision v1 - 项目整理工具")
    print("=" * 60)
    
    # 切换到项目根目录
    os.chdir(project_root)
    
    operations = []
    
    if args.all or args.clean:
        operations.append(("清理缓存", clean_cache_files))
    
    if args.all or args.logs:
        operations.append(("整理日志", organize_logs))
    
    # 总是检查项目结构
    operations.append(("检查结构", check_project_structure))
    
    if args.all or args.report:
        operations.append(("生成报告", generate_project_report))
    
    # 执行操作
    for name, func in operations:
        try:
            print(f"\n{name}...")
            result = func()
            print(f"✅ {name}完成")
        except Exception as e:
            print(f"❌ {name}失败: {e}")
    
    print("\n" + "=" * 60)
    print("🎉 项目整理完成！")

if __name__ == "__main__":
    main()
