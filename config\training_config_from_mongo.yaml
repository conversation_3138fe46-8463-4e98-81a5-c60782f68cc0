training_configs:
  config_112x112_no_norm:
    description: 112x112 不带归一化 - 减小网络规模
    enable: false
    hyperparameter_config: fine_tune
    model:
      depths:
      - 2
      - 3
      - 12
      - 2
      drop_path_rate: 0.2
      embed_dim: 128
      layer_scale_init_value: 0
      mlp_ratio: 2.0
      n_div: 4
      patch_size: 2
      patch_stride: 2
    name: 112x112_no_norm
    resources:
      batch_size: 128
      estimated_memory: 0.8
    transform_config: 112x112_no_norm
  config_112x112_norm:
    description: 112x112 带归一化 - 减小网络规模
    enable: false
    hyperparameter_config: aggressive
    model:
      depths:
      - 2
      - 3
      - 12
      - 2
      drop_path_rate: 0.2
      embed_dim: 128
      layer_scale_init_value: 0
      mlp_ratio: 2.0
      n_div: 4
      patch_size: 2
      patch_stride: 2
    name: 112x112_norm
    resources:
      batch_size: 128
      estimated_memory: 0.8
    transform_config: 112x112_norm
  config_224x224_no_norm:
    description: 224x224 不带归一化 - 标准配置
    enable: false
    hyperparameter_config: standard
    model:
      depths:
      - 3
      - 4
      - 18
      - 3
      drop_path_rate: 0.3
      embed_dim: 192
      layer_scale_init_value: 0
      mlp_ratio: 2.0
      n_div: 4
      patch_size: 4
      patch_stride: 4
    name: 224x224_no_norm
    resources:
      batch_size: 64
      estimated_memory: 1.2
    transform_config: 224x224_no_norm
  config_224x224_norm:
    description: 224x224 带归一化 - 标准配置，传统随机采样
    enable: false
    hyperparameter_config: conservative
    model:
      depths:
      - 3
      - 4
      - 18
      - 3
      drop_path_rate: 0.3
      embed_dim: 192
      layer_scale_init_value: 0
      mlp_ratio: 2.0
      n_div: 4
      patch_size: 4
      patch_stride: 4
    name: 224x224_norm
    resources:
      batch_size: 64
      estimated_memory: 1.2
    transform_config: 224x224_norm
  config_56x56_no_norm:
    description: 56x56 不带归一化 - 最小网络规模
    enable: false
    hyperparameter_config: stable
    model:
      depths:
      - 2
      - 2
      - 8
      - 2
      drop_path_rate: 0.1
      embed_dim: 64
      layer_scale_init_value: 0
      mlp_ratio: 2.0
      n_div: 2
      patch_size: 1
      patch_stride: 1
    name: 56x56_no_norm
    resources:
      batch_size: 256
      estimated_memory: 0.5
    transform_config: 56x56_no_norm
  config_56x56_norm:
    description: 56x56 带归一化 - 最小网络规模
    enable: false
    hyperparameter_config: adaptive
    model:
      depths:
      - 2
      - 2
      - 8
      - 2
      drop_path_rate: 0.1
      embed_dim: 64
      layer_scale_init_value: 0
      mlp_ratio: 2.0
      n_div: 2
      patch_size: 1
      patch_stride: 1
    name: 56x56_norm
    resources:
      batch_size: 256
      estimated_memory: 0.5
    transform_config: 56x56_norm
  config_balanced_112x112_small:
    dataset_config:
      sampling_strategy: original_level_sampling
      strategy_parameters:
        samples_per_original: 30
        target_originals: 20
        total_samples: 600
    description: 小规模平衡采样 - 112x112 带归一化 - 20个original × 30条 = 600张图
    enable: false
    hyperparameter_config: aggressive
    model:
      depths:
      - 2
      - 3
      - 12
      - 2
      drop_path_rate: 0.2
      embed_dim: 128
      layer_scale_init_value: 0
      mlp_ratio: 2.0
      n_div: 4
      patch_size: 2
      patch_stride: 2
    name: balanced_112x112_small
    resources:
      batch_size: 20
      estimated_memory: 0.8
    training:
      num_epochs: 80
    transform_config: 112x112_norm
  config_balanced_224x224_large:
    dataset_config:
      sampling_strategy: original_level_sampling
      strategy_parameters:
        samples_per_original: 80
        target_originals: 60
        total_samples: 4800
    description: 大规模平衡采样 - 224x224 带归一化 - 60个original × 80条 = 4800张图
    enable: false
    hyperparameter_config: stable
    model:
      depths:
      - 3
      - 4
      - 18
      - 3
      drop_path_rate: 0.4
      embed_dim: 256
      layer_scale_init_value: 0
      mlp_ratio: 2.0
      n_div: 4
      patch_size: 4
      patch_stride: 4
    name: balanced_224x224_large
    resources:
      batch_size: 60
      estimated_memory: 2.0
    training:
      num_epochs: 120
    transform_config: 224x224_norm
  config_original_balanced_224x224:
    dataset_config:
      sampling_strategy: original_level_sampling
      strategy_parameters:
        cross_subset_sampling: true
        samples_per_original: 60
        target_originals: 40
        test_ratio: 0.1
        total_samples: 2400
        train_ratio: 0.8
        val_ratio: 0.1
    description: Original级别平衡采样 - 224x224 带归一化 - 基于original_image字段的平衡采样策略
    enable: false
    hyperparameter_config: conservative
    model:
      depths:
      - 3
      - 4
      - 18
      - 3
      drop_path_rate: 0.3
      embed_dim: 192
      layer_scale_init_value: 0
      mlp_ratio: 2.0
      n_div: 4
      patch_size: 4
      patch_stride: 4
    name: original_balanced_224x224
    resources:
      batch_size: 40
      estimated_memory: 1.2
    training:
      num_epochs: 100
    transform_config: 224x224_norm
  config_random_sampling_224x224:
    dataset_config:
      sampling_strategy: random_sampling
      strategy_parameters:
        sample_size: 4500
        seed: 42
    description: 随机采样策略 - 224x224 带归一化 - 随机采样4500张图片
    enable: false
    hyperparameter_config: standard
    model:
      depths:
      - 3
      - 4
      - 18
      - 3
      drop_path_rate: 0.3
      embed_dim: 192
      layer_scale_init_value: 0
      mlp_ratio: 2.0
      n_div: 4
      patch_size: 4
      patch_stride: 4
    name: random_sampling_224x224
    resources:
      batch_size: 64
      estimated_memory: 1.2
    training:
      num_epochs: 80
    transform_config: 224x224_norm
  config_stratified_sampling_224x224:
    dataset_config:
      sampling_strategy: stratified_sampling
      strategy_parameters:
        maintain_distribution: true
        min_samples_per_stratum: 10
        total_samples: 4500
    description: 分层采样策略 - 224x224 带归一化 - 按标签分布分层采样
    enable: false
    hyperparameter_config: adaptive
    model:
      depths:
      - 3
      - 4
      - 18
      - 3
      drop_path_rate: 0.3
      embed_dim: 192
      layer_scale_init_value: 0
      mlp_ratio: 2.0
      n_div: 4
      patch_size: 4
      patch_stride: 4
    name: stratified_sampling_224x224
    resources:
      batch_size: 64
      estimated_memory: 1.2
    training:
      num_epochs: 90
    transform_config: 224x224_norm
  user_112x112_no_norm_high_lr:
    dataset_config:
      sampling_strategy: original_level_sampling
      strategy_parameters:
        cross_subset_sampling: true
        samples_per_original: 60
        target_originals: 20
        test_ratio: 0.1
        total_samples: 1200
        train_ratio: 0.8
        val_ratio: 0.1
    description: 用户配置 - 112x112不带归一化，学习率0.01，20原图×60样本
    enable: true
    hyperparameter_config: user_high_lr
    model:
      depths:
      - 2
      - 3
      - 12
      - 2
      drop_path_rate: 0.2
      embed_dim: 128
      layer_scale_init_value: 0
      mlp_ratio: 2.0
      n_div: 4
      patch_size: 2
      patch_stride: 2
    name: user_112x112_no_norm_high_lr
    resources:
      batch_size: 48
      estimated_memory: 0.8
    training:
      num_epochs: 100
      save_params: true
    transform_config: 112x112_no_norm
  user_112x112_no_norm_low_lr:
    dataset_config:
      sampling_strategy: original_level_sampling
      strategy_parameters:
        cross_subset_sampling: true
        samples_per_original: 60
        target_originals: 20
        test_ratio: 0.1
        total_samples: 1200
        train_ratio: 0.8
        val_ratio: 0.1
    description: 用户配置 - 112x112不带归一化，学习率0.0001，20原图×60样本
    enable: true
    hyperparameter_config: user_low_lr
    model:
      depths:
      - 2
      - 3
      - 12
      - 2
      drop_path_rate: 0.2
      embed_dim: 128
      layer_scale_init_value: 0
      mlp_ratio: 2.0
      n_div: 4
      patch_size: 2
      patch_stride: 2
    name: user_112x112_no_norm_low_lr
    resources:
      batch_size: 48
      estimated_memory: 0.8
    training:
      num_epochs: 100
      save_params: true
    transform_config: 112x112_no_norm
  user_112x112_no_norm_mid_lr:
    dataset_config:
      sampling_strategy: original_level_sampling
      strategy_parameters:
        cross_subset_sampling: true
        samples_per_original: 60
        target_originals: 20
        test_ratio: 0.1
        total_samples: 1200
        train_ratio: 0.8
        val_ratio: 0.1
    description: 用户配置 - 112x112不带归一化，学习率0.001，20原图×60样本
    enable: true
    hyperparameter_config: user_mid_lr
    model:
      depths:
      - 2
      - 3
      - 12
      - 2
      drop_path_rate: 0.2
      embed_dim: 128
      layer_scale_init_value: 0
      mlp_ratio: 2.0
      n_div: 4
      patch_size: 2
      patch_stride: 2
    name: user_112x112_no_norm_mid_lr
    resources:
      batch_size: 48
      estimated_memory: 0.8
    training:
      num_epochs: 100
      save_params: true
    transform_config: 112x112_no_norm
  user_112x112_no_norm_very_low_lr:
    dataset_config:
      sampling_strategy: original_level_sampling
      strategy_parameters:
        cross_subset_sampling: true
        samples_per_original: 60
        target_originals: 20
        test_ratio: 0.1
        total_samples: 1200
        train_ratio: 0.8
        val_ratio: 0.1
    description: 用户配置 - 112x112不带归一化，学习率0.00001，20原图×60样本
    enable: true
    hyperparameter_config: user_very_low_lr
    model:
      depths:
      - 2
      - 3
      - 12
      - 2
      drop_path_rate: 0.2
      embed_dim: 128
      layer_scale_init_value: 0
      mlp_ratio: 2.0
      n_div: 4
      patch_size: 2
      patch_stride: 2
    name: user_112x112_no_norm_very_low_lr
    resources:
      batch_size: 48
      estimated_memory: 0.8
    training:
      num_epochs: 100
      save_params: true
    transform_config: 112x112_no_norm
  user_112x112_norm_high_lr:
    dataset_config:
      sampling_strategy: original_level_sampling
      strategy_parameters:
        cross_subset_sampling: true
        samples_per_original: 60
        target_originals: 20
        test_ratio: 0.1
        total_samples: 1200
        train_ratio: 0.8
        val_ratio: 0.1
    description: 用户配置 - 112x112带归一化，学习率0.01，20原图×60样本
    enable: true
    hyperparameter_config: user_high_lr
    model:
      depths:
      - 2
      - 3
      - 12
      - 2
      drop_path_rate: 0.2
      embed_dim: 128
      layer_scale_init_value: 0
      mlp_ratio: 2.0
      n_div: 4
      patch_size: 2
      patch_stride: 2
    name: user_112x112_norm_high_lr
    resources:
      batch_size: 48
      estimated_memory: 0.8
    training:
      num_epochs: 100
      save_params: true
    transform_config: 112x112_norm
  user_112x112_norm_low_lr:
    dataset_config:
      sampling_strategy: original_level_sampling
      strategy_parameters:
        cross_subset_sampling: true
        samples_per_original: 60
        target_originals: 20
        test_ratio: 0.1
        total_samples: 1200
        train_ratio: 0.8
        val_ratio: 0.1
    description: 用户配置 - 112x112带归一化，学习率0.0001，20原图×60样本
    enable: true
    hyperparameter_config: user_low_lr
    model:
      depths:
      - 2
      - 3
      - 12
      - 2
      drop_path_rate: 0.2
      embed_dim: 128
      layer_scale_init_value: 0
      mlp_ratio: 2.0
      n_div: 4
      patch_size: 2
      patch_stride: 2
    name: user_112x112_norm_low_lr
    resources:
      batch_size: 48
      estimated_memory: 0.8
    training:
      num_epochs: 100
      save_params: true
    transform_config: 112x112_norm
  user_112x112_norm_mid_lr:
    dataset_config:
      sampling_strategy: original_level_sampling
      strategy_parameters:
        cross_subset_sampling: true
        samples_per_original: 60
        target_originals: 20
        test_ratio: 0.1
        total_samples: 1200
        train_ratio: 0.8
        val_ratio: 0.1
    description: 用户配置 - 112x112带归一化，学习率0.001，20原图×60样本
    enable: true
    hyperparameter_config: user_mid_lr
    model:
      depths:
      - 2
      - 3
      - 12
      - 2
      drop_path_rate: 0.2
      embed_dim: 128
      layer_scale_init_value: 0
      mlp_ratio: 2.0
      n_div: 4
      patch_size: 2
      patch_stride: 2
    name: user_112x112_norm_mid_lr
    resources:
      batch_size: 48
      estimated_memory: 0.8
    training:
      num_epochs: 100
      save_params: true
    transform_config: 112x112_norm
  user_112x112_norm_very_low_lr:
    dataset_config:
      sampling_strategy: original_level_sampling
      strategy_parameters:
        cross_subset_sampling: true
        samples_per_original: 60
        target_originals: 20
        test_ratio: 0.1
        total_samples: 1200
        train_ratio: 0.8
        val_ratio: 0.1
    description: 用户配置 - 112x112带归一化，学习率0.00001，20原图×60样本
    enable: true
    hyperparameter_config: user_very_low_lr
    model:
      depths:
      - 2
      - 3
      - 12
      - 2
      drop_path_rate: 0.2
      embed_dim: 128
      layer_scale_init_value: 0
      mlp_ratio: 2.0
      n_div: 4
      patch_size: 2
      patch_stride: 2
    name: user_112x112_norm_very_low_lr
    resources:
      batch_size: 48
      estimated_memory: 0.8
    training:
      num_epochs: 100
      save_params: true
    transform_config: 112x112_norm
  user_224x224_no_norm_high_lr:
    dataset_config:
      sampling_strategy: original_level_sampling
      strategy_parameters:
        cross_subset_sampling: true
        samples_per_original: 60
        target_originals: 20
        test_ratio: 0.1
        total_samples: 1200
        train_ratio: 0.8
        val_ratio: 0.1
    description: 用户配置 - 224x224不带归一化，高学习率0.01，20原图×60样本
    enable: true
    hyperparameter_config: user_high_lr
    model:
      depths:
      - 3
      - 4
      - 18
      - 3
      drop_path_rate: 0.3
      embed_dim: 192
      layer_scale_init_value: 0
      mlp_ratio: 2.0
      n_div: 4
      patch_size: 4
      patch_stride: 4
    name: user_224x224_no_norm_high_lr
    resources:
      batch_size: 32
      estimated_memory: 1.2
    training:
      num_epochs: 100
      save_params: true
    transform_config: 224x224_no_norm
  user_224x224_no_norm_low_lr:
    dataset_config:
      sampling_strategy: original_level_sampling
      strategy_parameters:
        cross_subset_sampling: true
        samples_per_original: 60
        target_originals: 20
        test_ratio: 0.1
        total_samples: 1200
        train_ratio: 0.8
        val_ratio: 0.1
    description: 用户配置 - 224x224不带归一化，低学习率0.0001，20原图×60样本
    enable: true
    hyperparameter_config: user_low_lr
    model:
      depths:
      - 3
      - 4
      - 18
      - 3
      drop_path_rate: 0.3
      embed_dim: 192
      layer_scale_init_value: 0
      mlp_ratio: 2.0
      n_div: 4
      patch_size: 4
      patch_stride: 4
    name: user_224x224_no_norm_low_lr
    resources:
      batch_size: 32
      estimated_memory: 1.2
    training:
      num_epochs: 100
      save_params: true
    transform_config: 224x224_no_norm
  user_224x224_no_norm_mid_lr:
    dataset_config:
      sampling_strategy: original_level_sampling
      strategy_parameters:
        cross_subset_sampling: true
        samples_per_original: 60
        target_originals: 20
        test_ratio: 0.1
        total_samples: 1200
        train_ratio: 0.8
        val_ratio: 0.1
    description: 用户配置 - 224x224不带归一化，中学习率0.001，20原图×60样本
    enable: true
    hyperparameter_config: user_mid_lr
    model:
      depths:
      - 3
      - 4
      - 18
      - 3
      drop_path_rate: 0.3
      embed_dim: 192
      layer_scale_init_value: 0
      mlp_ratio: 2.0
      n_div: 4
      patch_size: 4
      patch_stride: 4
    name: user_224x224_no_norm_mid_lr
    resources:
      batch_size: 32
      estimated_memory: 1.2
    training:
      num_epochs: 100
      save_params: true
    transform_config: 224x224_no_norm
  user_224x224_no_norm_very_low_lr:
    dataset_config:
      sampling_strategy: original_level_sampling
      strategy_parameters:
        cross_subset_sampling: true
        samples_per_original: 60
        target_originals: 20
        test_ratio: 0.1
        total_samples: 1200
        train_ratio: 0.8
        val_ratio: 0.1
    description: 用户配置 - 224x224不带归一化，极低学习率0.00001，20原图×60样本
    enable: true
    hyperparameter_config: user_very_low_lr
    model:
      depths:
      - 3
      - 4
      - 18
      - 3
      drop_path_rate: 0.3
      embed_dim: 192
      layer_scale_init_value: 0
      mlp_ratio: 2.0
      n_div: 4
      patch_size: 4
      patch_stride: 4
    name: user_224x224_no_norm_very_low_lr
    resources:
      batch_size: 32
      estimated_memory: 1.2
    training:
      num_epochs: 100
      save_params: true
    transform_config: 224x224_no_norm
  user_224x224_norm_high_lr:
    dataset_config:
      sampling_strategy: original_level_sampling
      strategy_parameters:
        cross_subset_sampling: true
        samples_per_original: 60
        target_originals: 20
        test_ratio: 0.1
        total_samples: 1200
        train_ratio: 0.8
        val_ratio: 0.1
    description: 用户配置 - 224x224带归一化，高学习率0.01，20原图×60样本
    enable: true
    hyperparameter_config: user_high_lr
    model:
      depths:
      - 3
      - 4
      - 18
      - 3
      drop_path_rate: 0.3
      embed_dim: 192
      layer_scale_init_value: 0
      mlp_ratio: 2.0
      n_div: 4
      patch_size: 4
      patch_stride: 4
    name: user_224x224_norm_high_lr
    resources:
      batch_size: 32
      estimated_memory: 1.2
    training:
      num_epochs: 100
      save_params: true
    transform_config: 224x224_norm
  user_224x224_norm_low_lr:
    dataset_config:
      sampling_strategy: original_level_sampling
      strategy_parameters:
        cross_subset_sampling: true
        samples_per_original: 60
        target_originals: 20
        test_ratio: 0.1
        total_samples: 1200
        train_ratio: 0.8
        val_ratio: 0.1
    description: 用户配置 - 224x224带归一化，低学习率0.0001，20原图×60样本
    enable: true
    hyperparameter_config: user_low_lr
    model:
      depths:
      - 3
      - 4
      - 18
      - 3
      drop_path_rate: 0.3
      embed_dim: 192
      layer_scale_init_value: 0
      mlp_ratio: 2.0
      n_div: 4
      patch_size: 4
      patch_stride: 4
    name: user_224x224_norm_low_lr
    resources:
      batch_size: 32
      estimated_memory: 1.2
    training:
      num_epochs: 100
      save_params: true
    transform_config: 224x224_norm
  user_224x224_norm_mid_lr:
    dataset_config:
      sampling_strategy: original_level_sampling
      strategy_parameters:
        cross_subset_sampling: true
        samples_per_original: 60
        target_originals: 20
        test_ratio: 0.1
        total_samples: 1200
        train_ratio: 0.8
        val_ratio: 0.1
    description: 用户配置 - 224x224带归一化，中学习率0.001，20原图×60样本
    enable: true
    hyperparameter_config: user_mid_lr
    model:
      depths:
      - 3
      - 4
      - 18
      - 3
      drop_path_rate: 0.3
      embed_dim: 192
      layer_scale_init_value: 0
      mlp_ratio: 2.0
      n_div: 4
      patch_size: 4
      patch_stride: 4
    name: user_224x224_norm_mid_lr
    resources:
      batch_size: 32
      estimated_memory: 1.2
    training:
      num_epochs: 100
      save_params: true
    transform_config: 224x224_norm
  user_224x224_norm_very_low_lr:
    dataset_config:
      sampling_strategy: original_level_sampling
      strategy_parameters:
        cross_subset_sampling: true
        samples_per_original: 60
        target_originals: 20
        test_ratio: 0.1
        total_samples: 1200
        train_ratio: 0.8
        val_ratio: 0.1
    description: 用户配置 - 224x224带归一化，极低学习率0.00001，20原图×60样本
    enable: true
    hyperparameter_config: user_very_low_lr
    model:
      depths:
      - 3
      - 4
      - 18
      - 3
      drop_path_rate: 0.3
      embed_dim: 192
      layer_scale_init_value: 0
      mlp_ratio: 2.0
      n_div: 4
      patch_size: 4
      patch_stride: 4
    name: user_224x224_norm_very_low_lr
    resources:
      batch_size: 32
      estimated_memory: 1.2
    training:
      num_epochs: 100
      save_params: true
    transform_config: 224x224_norm
  user_56x56_no_norm_high_lr:
    dataset_config:
      sampling_strategy: original_level_sampling
      strategy_parameters:
        cross_subset_sampling: true
        samples_per_original: 60
        target_originals: 20
        test_ratio: 0.1
        total_samples: 1200
        train_ratio: 0.8
        val_ratio: 0.1
    description: 用户配置 - 56x56不带归一化，学习率0.01，20原图×60样本
    enable: true
    hyperparameter_config: user_high_lr
    model:
      depths:
      - 2
      - 2
      - 6
      - 2
      drop_path_rate: 0.2
      embed_dim: 64
      layer_scale_init_value: 0
      mlp_ratio: 2.0
      n_div: 4
      patch_size: 1
      patch_stride: 1
    name: user_56x56_no_norm_high_lr
    resources:
      batch_size: 80
      estimated_memory: 0.4
    training:
      num_epochs: 100
      save_params: true
    transform_config: 56x56_no_norm
  user_56x56_no_norm_low_lr:
    dataset_config:
      sampling_strategy: original_level_sampling
      strategy_parameters:
        cross_subset_sampling: true
        samples_per_original: 60
        target_originals: 20
        test_ratio: 0.1
        total_samples: 1200
        train_ratio: 0.8
        val_ratio: 0.1
    description: 用户配置 - 56x56不带归一化，学习率0.0001，20原图×60样本
    enable: true
    hyperparameter_config: user_low_lr
    model:
      depths:
      - 2
      - 2
      - 6
      - 2
      drop_path_rate: 0.2
      embed_dim: 64
      layer_scale_init_value: 0
      mlp_ratio: 2.0
      n_div: 4
      patch_size: 1
      patch_stride: 1
    name: user_56x56_no_norm_low_lr
    resources:
      batch_size: 80
      estimated_memory: 0.4
    training:
      num_epochs: 100
      save_params: true
    transform_config: 56x56_no_norm
  user_56x56_no_norm_mid_lr:
    dataset_config:
      sampling_strategy: original_level_sampling
      strategy_parameters:
        cross_subset_sampling: true
        samples_per_original: 60
        target_originals: 20
        test_ratio: 0.1
        total_samples: 1200
        train_ratio: 0.8
        val_ratio: 0.1
    description: 用户配置 - 56x56不带归一化，学习率0.001，20原图×60样本
    enable: true
    hyperparameter_config: user_mid_lr
    model:
      depths:
      - 2
      - 2
      - 6
      - 2
      drop_path_rate: 0.2
      embed_dim: 64
      layer_scale_init_value: 0
      mlp_ratio: 2.0
      n_div: 4
      patch_size: 1
      patch_stride: 1
    name: user_56x56_no_norm_mid_lr
    resources:
      batch_size: 80
      estimated_memory: 0.4
    training:
      num_epochs: 100
      save_params: true
    transform_config: 56x56_no_norm
  user_56x56_no_norm_very_low_lr:
    dataset_config:
      sampling_strategy: original_level_sampling
      strategy_parameters:
        cross_subset_sampling: true
        samples_per_original: 60
        target_originals: 20
        test_ratio: 0.1
        total_samples: 1200
        train_ratio: 0.8
        val_ratio: 0.1
    description: 用户配置 - 56x56不带归一化，学习率0.00001，20原图×60样本
    enable: true
    hyperparameter_config: user_very_low_lr
    model:
      depths:
      - 2
      - 2
      - 6
      - 2
      drop_path_rate: 0.2
      embed_dim: 64
      layer_scale_init_value: 0
      mlp_ratio: 2.0
      n_div: 4
      patch_size: 1
      patch_stride: 1
    name: user_56x56_no_norm_very_low_lr
    resources:
      batch_size: 80
      estimated_memory: 0.4
    training:
      num_epochs: 100
      save_params: true
    transform_config: 56x56_no_norm
  user_56x56_norm_high_lr:
    dataset_config:
      sampling_strategy: original_level_sampling
      strategy_parameters:
        cross_subset_sampling: true
        samples_per_original: 60
        target_originals: 20
        test_ratio: 0.1
        total_samples: 1200
        train_ratio: 0.8
        val_ratio: 0.1
    description: 用户配置 - 56x56带归一化，学习率0.01，20原图×60样本
    enable: true
    hyperparameter_config: user_high_lr
    model:
      depths:
      - 2
      - 2
      - 6
      - 2
      drop_path_rate: 0.2
      embed_dim: 64
      layer_scale_init_value: 0
      mlp_ratio: 2.0
      n_div: 4
      patch_size: 1
      patch_stride: 1
    name: user_56x56_norm_high_lr
    resources:
      batch_size: 80
      estimated_memory: 0.4
    training:
      num_epochs: 100
      save_params: true
    transform_config: 56x56_norm
  user_56x56_norm_low_lr:
    dataset_config:
      sampling_strategy: original_level_sampling
      strategy_parameters:
        cross_subset_sampling: true
        samples_per_original: 60
        target_originals: 20
        test_ratio: 0.1
        total_samples: 1200
        train_ratio: 0.8
        val_ratio: 0.1
    description: 用户配置 - 56x56带归一化，学习率0.0001，20原图×60样本
    enable: true
    hyperparameter_config: user_low_lr
    model:
      depths:
      - 2
      - 2
      - 6
      - 2
      drop_path_rate: 0.2
      embed_dim: 64
      layer_scale_init_value: 0
      mlp_ratio: 2.0
      n_div: 4
      patch_size: 1
      patch_stride: 1
    name: user_56x56_norm_low_lr
    resources:
      batch_size: 80
      estimated_memory: 0.4
    training:
      num_epochs: 100
      save_params: true
    transform_config: 56x56_norm
  user_56x56_norm_mid_lr:
    dataset_config:
      sampling_strategy: original_level_sampling
      strategy_parameters:
        cross_subset_sampling: true
        samples_per_original: 60
        target_originals: 20
        test_ratio: 0.1
        total_samples: 1200
        train_ratio: 0.8
        val_ratio: 0.1
    description: 用户配置 - 56x56带归一化，学习率0.001，20原图×60样本
    enable: true
    hyperparameter_config: user_mid_lr
    model:
      depths:
      - 2
      - 2
      - 6
      - 2
      drop_path_rate: 0.2
      embed_dim: 64
      layer_scale_init_value: 0
      mlp_ratio: 2.0
      n_div: 4
      patch_size: 1
      patch_stride: 1
    name: user_56x56_norm_mid_lr
    resources:
      batch_size: 80
      estimated_memory: 0.4
    training:
      num_epochs: 100
      save_params: true
    transform_config: 56x56_norm
  user_56x56_norm_very_low_lr:
    dataset_config:
      sampling_strategy: original_level_sampling
      strategy_parameters:
        cross_subset_sampling: true
        samples_per_original: 60
        target_originals: 20
        test_ratio: 0.1
        total_samples: 1200
        train_ratio: 0.8
        val_ratio: 0.1
    description: 用户配置 - 56x56带归一化，学习率0.00001，20原图×60样本
    enable: true
    hyperparameter_config: user_very_low_lr
    model:
      depths:
      - 2
      - 2
      - 6
      - 2
      drop_path_rate: 0.2
      embed_dim: 64
      layer_scale_init_value: 0
      mlp_ratio: 2.0
      n_div: 4
      patch_size: 1
      patch_stride: 1
    name: user_56x56_norm_very_low_lr
    resources:
      batch_size: 80
      estimated_memory: 0.4
    training:
      num_epochs: 100
      save_params: true
    transform_config: 56x56_norm
  user_80x80_no_norm_high_lr:
    dataset_config:
      sampling_strategy: original_level_sampling
      strategy_parameters:
        cross_subset_sampling: true
        samples_per_original: 60
        target_originals: 20
        test_ratio: 0.1
        total_samples: 1200
        train_ratio: 0.8
        val_ratio: 0.1
    description: 用户配置 - 80x80不带归一化，学习率0.01，20原图×60样本
    enable: true
    hyperparameter_config: user_high_lr
    model:
      depths:
      - 2
      - 3
      - 8
      - 2
      drop_path_rate: 0.2
      embed_dim: 96
      layer_scale_init_value: 0
      mlp_ratio: 2.0
      n_div: 4
      patch_size: 2
      patch_stride: 2
    name: user_80x80_no_norm_high_lr
    resources:
      batch_size: 64
      estimated_memory: 0.6
    training:
      num_epochs: 100
      save_params: true
    transform_config: 80x80_no_norm
  user_80x80_no_norm_low_lr:
    dataset_config:
      sampling_strategy: original_level_sampling
      strategy_parameters:
        cross_subset_sampling: true
        samples_per_original: 60
        target_originals: 20
        test_ratio: 0.1
        total_samples: 1200
        train_ratio: 0.8
        val_ratio: 0.1
    description: 用户配置 - 80x80不带归一化，学习率0.0001，20原图×60样本
    enable: true
    hyperparameter_config: user_low_lr
    model:
      depths:
      - 2
      - 3
      - 8
      - 2
      drop_path_rate: 0.2
      embed_dim: 96
      layer_scale_init_value: 0
      mlp_ratio: 2.0
      n_div: 4
      patch_size: 2
      patch_stride: 2
    name: user_80x80_no_norm_low_lr
    resources:
      batch_size: 64
      estimated_memory: 0.6
    training:
      num_epochs: 100
      save_params: true
    transform_config: 80x80_no_norm
  user_80x80_no_norm_mid_lr:
    dataset_config:
      sampling_strategy: original_level_sampling
      strategy_parameters:
        cross_subset_sampling: true
        samples_per_original: 60
        target_originals: 20
        test_ratio: 0.1
        total_samples: 1200
        train_ratio: 0.8
        val_ratio: 0.1
    description: 用户配置 - 80x80不带归一化，学习率0.001，20原图×60样本
    enable: true
    hyperparameter_config: user_mid_lr
    model:
      depths:
      - 2
      - 3
      - 8
      - 2
      drop_path_rate: 0.2
      embed_dim: 96
      layer_scale_init_value: 0
      mlp_ratio: 2.0
      n_div: 4
      patch_size: 2
      patch_stride: 2
    name: user_80x80_no_norm_mid_lr
    resources:
      batch_size: 64
      estimated_memory: 0.6
    training:
      num_epochs: 100
      save_params: true
    transform_config: 80x80_no_norm
  user_80x80_no_norm_very_low_lr:
    dataset_config:
      sampling_strategy: original_level_sampling
      strategy_parameters:
        cross_subset_sampling: true
        samples_per_original: 60
        target_originals: 20
        test_ratio: 0.1
        total_samples: 1200
        train_ratio: 0.8
        val_ratio: 0.1
    description: 用户配置 - 80x80不带归一化，学习率0.00001，20原图×60样本
    enable: true
    hyperparameter_config: user_very_low_lr
    model:
      depths:
      - 2
      - 3
      - 8
      - 2
      drop_path_rate: 0.2
      embed_dim: 96
      layer_scale_init_value: 0
      mlp_ratio: 2.0
      n_div: 4
      patch_size: 2
      patch_stride: 2
    name: user_80x80_no_norm_very_low_lr
    resources:
      batch_size: 64
      estimated_memory: 0.6
    training:
      num_epochs: 100
      save_params: true
    transform_config: 80x80_no_norm
  user_80x80_norm_high_lr:
    dataset_config:
      sampling_strategy: original_level_sampling
      strategy_parameters:
        cross_subset_sampling: true
        samples_per_original: 60
        target_originals: 20
        test_ratio: 0.1
        total_samples: 1200
        train_ratio: 0.8
        val_ratio: 0.1
    description: 用户配置 - 80x80带归一化，学习率0.01，20原图×60样本
    enable: true
    hyperparameter_config: user_high_lr
    model:
      depths:
      - 2
      - 3
      - 8
      - 2
      drop_path_rate: 0.2
      embed_dim: 96
      layer_scale_init_value: 0
      mlp_ratio: 2.0
      n_div: 4
      patch_size: 2
      patch_stride: 2
    name: user_80x80_norm_high_lr
    resources:
      batch_size: 64
      estimated_memory: 0.6
    training:
      num_epochs: 100
      save_params: true
    transform_config: 80x80_norm
  user_80x80_norm_low_lr:
    dataset_config:
      sampling_strategy: original_level_sampling
      strategy_parameters:
        cross_subset_sampling: true
        samples_per_original: 60
        target_originals: 20
        test_ratio: 0.1
        total_samples: 1200
        train_ratio: 0.8
        val_ratio: 0.1
    description: 用户配置 - 80x80带归一化，学习率0.0001，20原图×60样本
    enable: true
    hyperparameter_config: user_low_lr
    model:
      depths:
      - 2
      - 3
      - 8
      - 2
      drop_path_rate: 0.2
      embed_dim: 96
      layer_scale_init_value: 0
      mlp_ratio: 2.0
      n_div: 4
      patch_size: 2
      patch_stride: 2
    name: user_80x80_norm_low_lr
    resources:
      batch_size: 64
      estimated_memory: 0.6
    training:
      num_epochs: 100
      save_params: true
    transform_config: 80x80_norm
  user_80x80_norm_mid_lr:
    dataset_config:
      sampling_strategy: original_level_sampling
      strategy_parameters:
        cross_subset_sampling: true
        samples_per_original: 60
        target_originals: 20
        test_ratio: 0.1
        total_samples: 1200
        train_ratio: 0.8
        val_ratio: 0.1
    description: 用户配置 - 80x80带归一化，学习率0.001，20原图×60样本
    enable: true
    hyperparameter_config: user_mid_lr
    model:
      depths:
      - 2
      - 3
      - 8
      - 2
      drop_path_rate: 0.2
      embed_dim: 96
      layer_scale_init_value: 0
      mlp_ratio: 2.0
      n_div: 4
      patch_size: 2
      patch_stride: 2
    name: user_80x80_norm_mid_lr
    resources:
      batch_size: 64
      estimated_memory: 0.6
    training:
      num_epochs: 100
      save_params: true
    transform_config: 80x80_norm
  user_80x80_norm_very_low_lr:
    dataset_config:
      sampling_strategy: original_level_sampling
      strategy_parameters:
        cross_subset_sampling: true
        samples_per_original: 60
        target_originals: 20
        test_ratio: 0.1
        total_samples: 1200
        train_ratio: 0.8
        val_ratio: 0.1
    description: 用户配置 - 80x80带归一化，学习率0.00001，20原图×60样本
    enable: true
    hyperparameter_config: user_very_low_lr
    model:
      depths:
      - 2
      - 3
      - 8
      - 2
      drop_path_rate: 0.2
      embed_dim: 96
      layer_scale_init_value: 0
      mlp_ratio: 2.0
      n_div: 4
      patch_size: 2
      patch_stride: 2
    name: user_80x80_norm_very_low_lr
    resources:
      batch_size: 64
      estimated_memory: 0.6
    training:
      num_epochs: 100
      save_params: true
    transform_config: 80x80_norm
