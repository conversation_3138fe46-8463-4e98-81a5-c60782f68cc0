#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SeedVision v1 - 测试套件主入口

提供统一的测试入口，支持不同级别的测试：
1. quick - 快速测试（2-3分钟）
2. basic - 基础测试（5-10分钟）
3. full - 完整测试（15-30分钟）
4. training - 训练功能测试
5. scheduler - 调度器测试

使用方法：
python run_tests.py [quick|basic|full|training|scheduler]
"""

import sys
import os
import argparse
import subprocess
import time
from datetime import datetime

# 添加路径
# 添加项目根路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(project_root)
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 颜色定义
class Colors:
    RED = '\033[91m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    MAGENTA = '\033[95m'
    CYAN = '\033[96m'
    WHITE = '\033[97m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'
    END = '\033[0m'

def run_command(command, description):
    """运行命令并返回结果"""
    print(f"\n{Colors.BLUE}[RUNNING]{Colors.END} {description}")
    print("=" * 60)

    start_time = time.time()

    try:
        # 运行命令
        result = subprocess.run(
            [sys.executable] + command.split()[1:],
            capture_output=True,
            text=True,
            cwd=os.path.dirname(os.path.abspath(__file__))
        )

        duration = time.time() - start_time

        # 显示输出
        if result.stdout:
            print(result.stdout)

        if result.stderr and result.returncode != 0:
            print("错误输出:")
            print(result.stderr)

        success = result.returncode == 0

        status_text = f"{Colors.GREEN}[SUCCESS]{Colors.END}" if success else f"{Colors.RED}[FAILED]{Colors.END}"
        print(f"\n{status_text} - 耗时: {duration:.1f}秒")

        return success, duration

    except Exception as e:
        duration = time.time() - start_time
        print(f"{Colors.RED}[ERROR]{Colors.END} 执行失败: {e}")
        return False, duration

def quick_test():
    """快速测试"""
    print(f"{Colors.CYAN}[QUICK TEST]{Colors.END} SeedVision v1 - 快速测试套件")
    print("=" * 80)
    print("测试范围: 基础导入、配置、文件结构")
    print("预计耗时: 2-3分钟")

    success, duration = run_command("python quick_test.py", "快速功能测试")

    return success, duration

def basic_test():
    """基础测试"""
    print(f"{Colors.YELLOW}[BASIC TEST]{Colors.END} SeedVision v1 - 基础测试套件")
    print("=" * 80)
    print("测试范围: 导入、配置、数据、模型、调度器")
    print("预计耗时: 5-10分钟")

    total_duration = 0
    all_success = True

    # 快速测试
    success, duration = run_command("python quick_test.py", "快速功能测试")
    all_success &= success
    total_duration += duration

    # 调度器测试
    success, duration = run_command("python main_scheduler.py --mode estimate", "调度器资源预估测试")
    all_success &= success
    total_duration += duration

    return all_success, total_duration

def training_test():
    """训练功能测试"""
    print(f"{Colors.MAGENTA}[TRAINING TEST]{Colors.END} SeedVision v1 - 训练功能测试套件")
    print("=" * 80)
    print("测试范围: 数据采样、Original评估、可视化、快速训练")
    print("预计耗时: 10-15分钟")

    success, duration = run_command("python training_test.py --full", "训练功能完整测试")

    return success, duration

def scheduler_test():
    """调度器测试"""
    print(f"{Colors.BLUE}[SCHEDULER TEST]{Colors.END} SeedVision v1 - 调度器测试套件")
    print("=" * 80)
    print("测试范围: 资源预估、任务调度、系统监控")
    print("预计耗时: 3-5分钟")

    total_duration = 0
    all_success = True

    # 资源预估测试
    success, duration = run_command("python main_scheduler.py --mode estimate", "资源预估测试")
    all_success &= success
    total_duration += duration

    # 调度器示例测试
    success, duration = run_command("python tests/examples/scheduler_example.py", "调度器示例测试")
    all_success &= success
    total_duration += duration

    return all_success, total_duration

def full_test():
    """完整测试"""
    print(f"{Colors.GREEN}[FULL TEST]{Colors.END} SeedVision v1 - 完整测试套件")
    print("=" * 80)
    print("测试范围: 所有功能的完整测试")
    print("预计耗时: 15-30分钟")

    total_duration = 0
    all_success = True

    # 系统完整测试
    success, duration = run_command("python system_test.py", "系统完整测试")
    all_success &= success
    total_duration += duration

    # 训练功能测试
    success, duration = run_command("python training_test.py --full", "训练功能测试")
    all_success &= success
    total_duration += duration

    return all_success, total_duration

def show_test_menu():
    """显示测试菜单"""
    print(f"{Colors.BOLD}[TEST SUITE]{Colors.END} SeedVision v1 - 测试套件")
    print("=" * 50)
    print("可用的测试选项:")
    print()
    print(f"{Colors.CYAN}1. quick{Colors.END}     - 快速测试 (2-3分钟)")
    print("   └─ 基础导入、配置、文件结构")
    print()
    print(f"{Colors.YELLOW}2. basic{Colors.END}     - 基础测试 (5-10分钟)")
    print("   └─ 快速测试 + 调度器功能")
    print()
    print(f"{Colors.MAGENTA}3. training{Colors.END}  - 训练测试 (10-15分钟)")
    print("   └─ 数据采样、训练流程、Original评估")
    print()
    print(f"{Colors.BLUE}4. scheduler{Colors.END} - 调度器测试 (3-5分钟)")
    print("   └─ 资源预估、任务调度、系统监控")
    print()
    print(f"{Colors.GREEN}5. full{Colors.END}      - 完整测试 (15-30分钟)")
    print("   └─ 所有功能的完整测试")
    print()
    print("使用方法:")
    print("  python run_tests.py [test_type]")
    print("  例如: python run_tests.py quick")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='SeedVision v1 测试套件')
    parser.add_argument('test_type', nargs='?',
                       choices=['quick', 'basic', 'training', 'scheduler', 'full'],
                       help='测试类型')
    parser.add_argument('--list', action='store_true', help='显示可用的测试选项')

    args = parser.parse_args()

    if args.list or not args.test_type:
        show_test_menu()
        return

    start_time = datetime.now()
    print(f"开始时间: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")

    # 根据测试类型运行相应测试
    test_functions = {
        'quick': quick_test,
        'basic': basic_test,
        'training': training_test,
        'scheduler': scheduler_test,
        'full': full_test
    }

    test_func = test_functions[args.test_type]

    try:
        success, duration = test_func()

        end_time = datetime.now()
        total_duration = (end_time - start_time).total_seconds()

        print("\n" + "=" * 80)
        print(f"{Colors.BOLD}[COMPLETE]{Colors.END} 测试套件执行完成")
        print("=" * 80)
        print(f"测试类型: {args.test_type}")
        print(f"开始时间: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"结束时间: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"总耗时: {total_duration:.1f}秒 ({total_duration/60:.1f}分钟)")

        if success:
            print(f"测试结果: {Colors.GREEN}[SUCCESS]{Colors.END} 成功")
            print(f"\n{Colors.GREEN}[PASSED]{Colors.END} 所有测试通过！系统运行正常。")
            print(f"\n{Colors.BLUE}[INFO]{Colors.END} 下一步建议:")
            if args.test_type == 'quick':
                print("   - 运行基础测试: python run_tests.py basic")
            elif args.test_type == 'basic':
                print("   - 运行训练测试: python run_tests.py training")
            elif args.test_type in ['training', 'scheduler']:
                print("   - 运行完整测试: python run_tests.py full")
            print("   - 开始使用系统: python main.py --sequential")
        else:
            print(f"测试结果: {Colors.RED}[FAILED]{Colors.END} 失败")
            print(f"\n{Colors.YELLOW}[WARNING]{Colors.END} 部分测试失败，请检查上述错误信息。")
            print(f"{Colors.BLUE}[INFO]{Colors.END} 建议先修复失败的测试项，然后重新运行测试。")

        sys.exit(0 if success else 1)

    except KeyboardInterrupt:
        print(f"\n\n{Colors.YELLOW}[INTERRUPTED]{Colors.END} 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n{Colors.RED}[ERROR]{Colors.END} 测试执行过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
