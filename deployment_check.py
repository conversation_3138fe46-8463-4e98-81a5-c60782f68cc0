#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SeedVisionTrain 部署检查脚本

确保项目可以在任何环境中独立部署和运行
"""

import os
import sys
import subprocess
import importlib

def check_python_version():
    """检查Python版本"""
    print("🐍 检查Python版本")
    print("-" * 30)
    
    version = sys.version_info
    print(f"当前Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major >= 3 and version.minor >= 8:
        print("✅ Python版本符合要求 (>= 3.8)")
        return True
    else:
        print("❌ Python版本过低，需要 >= 3.8")
        return False

def check_dependencies():
    """检查依赖包"""
    print("\n📦 检查依赖包")
    print("-" * 30)
    
    required_packages = [
        'torch',
        'torchvision', 
        'numpy',
        'pandas',
        'matplotlib',
        'tqdm',
        'PIL',
        'cv2',
        'pymongo',
        'pymysql',
        'yaml',
        'ultralytics',
        'GPUtil'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'PIL':
                importlib.import_module('PIL')
            elif package == 'cv2':
                importlib.import_module('cv2')
            elif package == 'yaml':
                importlib.import_module('yaml')
            else:
                importlib.import_module(package)
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package}")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️  缺失依赖包: {', '.join(missing_packages)}")
        print("安装命令:")
        if 'torch' in missing_packages:
            print("pip install torch torchvision torchaudio")
        if 'PIL' in missing_packages:
            print("pip install pillow")
        if 'cv2' in missing_packages:
            print("pip install opencv-python")
        if 'yaml' in missing_packages:
            print("pip install pyyaml")
        for pkg in missing_packages:
            if pkg not in ['torch', 'torchvision', 'PIL', 'cv2', 'yaml']:
                print(f"pip install {pkg}")
        return False
    else:
        print("✅ 所有依赖包都已安装")
        return True

def check_project_structure():
    """检查项目结构"""
    print("\n📁 检查项目结构")
    print("-" * 30)
    
    required_structure = {
        'files': [
            'main.py',
            'README_INDEPENDENT.md',
            'verify_independence.py',
            'deployment_check.py'
        ],
        'directories': [
            'utils',
            'config', 
            'models',
            'tools',
            'scheduler',
            'runners',
            'tests',
            'output'
        ],
        'key_files': [
            'utils/__init__.py',
            'utils/logger.py',
            'utils/db_utils.py',
            'config/config_loader.py',
            'config/training_config.yaml',
            'tools/training/train.py',
            'models/FasterNet.py',
            'runners/training/main.py'
        ]
    }
    
    missing_items = []
    
    # 检查文件
    for file_path in required_structure['files']:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path}")
            missing_items.append(file_path)
    
    # 检查目录
    for dir_path in required_structure['directories']:
        if os.path.isdir(dir_path):
            print(f"✅ {dir_path}/")
        else:
            print(f"❌ {dir_path}/")
            missing_items.append(f"{dir_path}/")
    
    # 检查关键文件
    for file_path in required_structure['key_files']:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path}")
            missing_items.append(file_path)
    
    if missing_items:
        print(f"\n⚠️  缺失项目文件: {len(missing_items)} 个")
        return False
    else:
        print("✅ 项目结构完整")
        return True

def test_basic_imports():
    """测试基础导入"""
    print("\n🔗 测试基础导入")
    print("-" * 30)
    
    # 添加当前目录到路径
    sys.path.insert(0, '.')
    
    test_imports = [
        ("utils", "import utils"),
        ("utils.logger", "from utils.logger import logger"),
        ("utils.db_utils", "from utils.db_utils import database_connecter"),
        ("config.config_loader", "from config.config_loader import ConfigLoader"),
        ("models.FasterNet", "from models.FasterNet import model"),
        ("tools.training.train", "from tools.training.train import train_model"),
    ]
    
    success_count = 0
    
    for module_name, import_statement in test_imports:
        try:
            exec(import_statement)
            print(f"✅ {module_name}")
            success_count += 1
        except Exception as e:
            print(f"❌ {module_name}: {str(e)[:50]}...")
    
    if success_count == len(test_imports):
        print("✅ 所有基础模块导入成功")
        return True
    else:
        print(f"⚠️  {len(test_imports) - success_count} 个模块导入失败")
        return False

def test_main_entry():
    """测试主入口"""
    print("\n🚪 测试主入口")
    print("-" * 30)
    
    try:
        # 测试main.py是否可以导入
        import main
        print("✅ main.py 导入成功")
        
        # 检查main函数是否存在
        if hasattr(main, 'main'):
            print("✅ main() 函数存在")
            return True
        else:
            print("❌ main() 函数不存在")
            return False
            
    except Exception as e:
        print(f"❌ main.py 导入失败: {e}")
        return False

def generate_deployment_report():
    """生成部署报告"""
    print("\n📋 生成部署报告")
    print("-" * 30)
    
    report = {
        'timestamp': __import__('datetime').datetime.now().isoformat(),
        'python_version': f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}",
        'platform': sys.platform,
        'working_directory': os.getcwd(),
        'project_size': get_project_size(),
    }
    
    try:
        import json
        with open('deployment_report.json', 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        print("✅ 部署报告已生成: deployment_report.json")
        return True
    except Exception as e:
        print(f"❌ 生成部署报告失败: {e}")
        return False

def get_project_size():
    """计算项目大小"""
    total_size = 0
    file_count = 0
    
    for root, dirs, files in os.walk('.'):
        # 跳过__pycache__和.git目录
        dirs[:] = [d for d in dirs if d not in ['__pycache__', '.git', '.vscode']]
        
        for file in files:
            if not file.endswith('.pyc'):
                file_path = os.path.join(root, file)
                try:
                    total_size += os.path.getsize(file_path)
                    file_count += 1
                except:
                    pass
    
    return {
        'total_size_mb': round(total_size / (1024 * 1024), 2),
        'file_count': file_count
    }

def main():
    """主函数"""
    print("🚀 SeedVisionTrain 部署检查")
    print("=" * 60)
    
    # 切换到脚本所在目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    
    # 执行检查
    checks = [
        ("Python版本", check_python_version),
        ("依赖包", check_dependencies),
        ("项目结构", check_project_structure),
        ("基础导入", test_basic_imports),
        ("主入口", test_main_entry),
        ("部署报告", generate_deployment_report),
    ]
    
    passed_checks = 0
    
    for check_name, check_func in checks:
        try:
            if check_func():
                passed_checks += 1
        except Exception as e:
            print(f"❌ {check_name} 检查异常: {e}")
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 部署检查总结")
    print("=" * 60)
    
    print(f"通过检查: {passed_checks}/{len(checks)}")
    
    if passed_checks == len(checks):
        print("🎉 项目已准备好部署！")
        print("\n🚀 启动命令:")
        print("python main.py")
        return True
    else:
        print("⚠️  项目需要修复后才能部署")
        print("\n🔧 修复建议:")
        print("1. 安装缺失的依赖包")
        print("2. 检查项目文件完整性")
        print("3. 运行 python verify_independence.py")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
