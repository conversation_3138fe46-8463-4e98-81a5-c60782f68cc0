#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SeedVision v1 - 智能调度器快捷入口

这是调度器的快捷入口文件，直接调用 runners/training/main_scheduler.py

使用方法：
python main_scheduler.py --mode estimate  # 资源预估模式
python main_scheduler.py --mode schedule  # 调度训练模式
python main_scheduler.py --mode monitor   # 监控模式
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.append(project_root)

if __name__ == "__main__":
    # 直接调用实际的调度器主程序
    from runners.training.main_scheduler import main
    main()
