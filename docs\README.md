# SeedVision v1 - 文档索引

## 📚 文档目录

### 🎯 核心文档
- [PROJECT_STATUS.md](PROJECT_STATUS.md) - 项目状态总结
- [design.md](design.md) - 系统设计文档
- [OPTIMIZATION_SUMMARY.md](OPTIMIZATION_SUMMARY.md) - 系统优化总结
- [ORIGINAL_LEVEL_EVALUATION.md](ORIGINAL_LEVEL_EVALUATION.md) - Original级别评估功能
- [SAMPLING_STRATEGY_REFACTOR.md](SAMPLING_STRATEGY_REFACTOR.md) - 采样策略重构
- [SCHEDULER_SUMMARY.md](SCHEDULER_SUMMARY.md) - 智能调度系统

### 📖 使用指南
- [../config/CONFIG_GUIDE.md](../config/CONFIG_GUIDE.md) - 配置使用指南
- [../config/SAMPLING_STRATEGIES.md](../config/SAMPLING_STRATEGIES.md) - 采样策略详解
- [../scheduler/README.md](../scheduler/README.md) - 调度器使用指南

### 🧪 测试文档
- [TESTING_GUIDE.md](TESTING_GUIDE.md) - 完整测试指南
- [TESTING_COMPLETE.md](TESTING_COMPLETE.md) - 测试系统实现总结
- [../tests/README.md](../tests/README.md) - 测试说明

### 🔧 开发文档
- [FILE_ORGANIZATION.md](FILE_ORGANIZATION.md) - 文件整理方案
- [FILE_ORGANIZATION_COMPLETE.md](FILE_ORGANIZATION_COMPLETE.md) - 文件整理完成报告

## 🔍 快速导航

### 新手入门
1. 阅读 [design.md](design.md) 了解系统架构
2. 查看 [../config/CONFIG_GUIDE.md](../config/CONFIG_GUIDE.md) 学习配置
3. 运行 [../tests/examples/](../tests/examples/) 中的示例

### 功能使用
- **配置训练**：参考 [../config/CONFIG_GUIDE.md](../config/CONFIG_GUIDE.md)
- **采样策略**：参考 [../config/SAMPLING_STRATEGIES.md](../config/SAMPLING_STRATEGIES.md)
- **智能调度**：参考 [../scheduler/README.md](../scheduler/README.md)
- **Original级别评估**：参考 [ORIGINAL_LEVEL_EVALUATION.md](ORIGINAL_LEVEL_EVALUATION.md)

### 开发参考
- **系统优化**：参考 [OPTIMIZATION_SUMMARY.md](OPTIMIZATION_SUMMARY.md)
- **架构重构**：参考 [SAMPLING_STRATEGY_REFACTOR.md](SAMPLING_STRATEGY_REFACTOR.md)
- **调度系统**：参考 [SCHEDULER_SUMMARY.md](SCHEDULER_SUMMARY.md)

## 📝 文档更新

文档按功能模块组织，便于查找和维护。如需添加新文档，请按照相应的功能分类放置。
