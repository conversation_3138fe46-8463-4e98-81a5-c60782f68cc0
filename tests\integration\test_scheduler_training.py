#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SeedVision v1 - 调度器训练真实测试

功能：
1. 创建测试配置文件
2. 启动调度器进行真实训练
3. 监控训练过程
4. 验证训练结果
5. 生成测试报告

使用方法：
python tests/integration/test_scheduler_training.py [--quick] [--full]
"""

import os
import sys
import yaml
import time
import argparse
import shutil
from datetime import datetime
from pathlib import Path

# 添加项目根路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(project_root)

from config.config_loader import ConfigLoader
from scheduler import SchedulerManager, TrainingTask, TaskPriority

class Colors:
    RED = '\033[91m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    MAGENTA = '\033[95m'
    CYAN = '\033[96m'
    WHITE = '\033[97m'
    BOLD = '\033[1m'
    END = '\033[0m'

class SchedulerTrainingTest:
    """调度器训练测试类"""

    def __init__(self, test_mode='quick'):
        """
        初始化测试

        参数:
            test_mode: 测试模式 ('quick' 或 'full')
        """
        self.test_mode = test_mode
        self.test_start_time = datetime.now()
        self.test_id = f"scheduler_test_{self.test_start_time.strftime('%Y%m%d_%H%M%S')}"

        # 测试目录
        self.test_dir = os.path.join(project_root, "tests", "output", self.test_id)
        os.makedirs(self.test_dir, exist_ok=True)

        # 测试配置文件
        self.test_config_file = os.path.join(self.test_dir, "test_config.yaml")

        # 测试结果
        self.test_results = {
            'test_id': self.test_id,
            'test_mode': test_mode,
            'start_time': self.test_start_time.isoformat(),
            'configs_tested': [],
            'scheduler_stats': {},
            'training_results': {},
            'success': False,
            'error_message': None
        }

        print(f"{Colors.BOLD}🧪 调度器训练测试{Colors.END}")
        print(f"测试ID: {self.test_id}")
        print(f"测试模式: {test_mode}")
        print(f"测试目录: {self.test_dir}")

    def create_test_config(self):
        """创建测试配置文件"""
        print(f"\n{Colors.BLUE}[SETUP]{Colors.END} 创建测试配置文件")

        if self.test_mode == 'quick':
            # 快速测试：只启用2个小配置，训练5个epoch
            test_configs = {
                'user_56x56_no_norm_high_lr': {
                    'enable': True,
                    'training': {'num_epochs': 5}  # 快速测试只训练5个epoch
                },
                'user_112x112_no_norm_low_lr': {
                    'enable': True,
                    'training': {'num_epochs': 5}  # 快速测试只训练5个epoch
                }
            }
        else:
            # 完整测试：启用4个配置，训练10个epoch
            test_configs = {
                'user_56x56_no_norm_high_lr': {
                    'enable': True,
                    'training': {'num_epochs': 10}
                },
                'user_112x112_no_norm_low_lr': {
                    'enable': True,
                    'training': {'num_epochs': 10}
                },
                'user_112x112_no_norm_high_lr': {
                    'enable': True,
                    'training': {'num_epochs': 10}
                },
                'config_56x56_norm': {
                    'enable': True,
                    'training': {'num_epochs': 10}
                }
            }

        # 加载原始配置
        original_config_file = os.path.join(project_root, "config", "training_config.yaml")
        with open(original_config_file, 'r', encoding='utf-8') as f:
            config_data = yaml.safe_load(f)

        # 禁用所有配置
        for config_name in config_data['training_configs']:
            config_data['training_configs'][config_name]['enable'] = False

        # 启用测试配置
        for config_name, config_updates in test_configs.items():
            if config_name in config_data['training_configs']:
                config_data['training_configs'][config_name].update(config_updates)
                self.test_results['configs_tested'].append(config_name)
                print(f"  ✅ 启用配置: {config_name}")
            else:
                print(f"  ⚠️  配置不存在: {config_name}")

        # 保存测试配置文件
        with open(self.test_config_file, 'w', encoding='utf-8') as f:
            yaml.dump(config_data, f, default_flow_style=False, allow_unicode=True)

        print(f"  📁 测试配置文件: {self.test_config_file}")
        print(f"  📊 启用配置数量: {len(self.test_results['configs_tested'])}")

    def run_scheduler_training(self):
        """运行调度器训练"""
        print(f"\n{Colors.GREEN}[TRAINING]{Colors.END} 启动调度器训练")

        try:
            # 加载测试配置
            config_loader = ConfigLoader(self.test_config_file)
            configs = config_loader.get_enabled_training_configs()

            if not configs:
                raise Exception("没有找到启用的训练配置")

            print(f"  📋 加载配置数量: {len(configs)}")

            # 创建调度管理器
            max_memory = 6.0 if self.test_mode == 'quick' else 8.0
            max_concurrent = 1 if self.test_mode == 'quick' else 2

            scheduler = SchedulerManager(
                max_gpu_memory=max_memory,
                max_concurrent_tasks=max_concurrent,
                log_dir=os.path.join(self.test_dir, "logs")
            )

            print(f"  🚀 调度器配置: 最大显存={max_memory}GB, 最大并发={max_concurrent}")

            # 启动调度器
            scheduler.start()

            # 创建训练任务
            tasks = []
            for i, config in enumerate(configs):
                task = TrainingTask(
                    task_id=f"test_task_{i+1:02d}",
                    name=config['name'],
                    config=config,
                    priority=TaskPriority.HIGH,  # 测试任务使用高优先级
                    on_start=self._on_task_start,
                    on_complete=self._on_task_complete,
                    on_error=self._on_task_error
                )

                tasks.append(task)
                task_id = scheduler.submit_task(task)
                print(f"  📤 提交任务: {config['name']} (ID: {task_id})")

            # 监控训练过程
            print(f"\n{Colors.CYAN}[MONITORING]{Colors.END} 监控训练过程")

            start_time = time.time()
            max_wait_time = 1800 if self.test_mode == 'quick' else 3600  # 快速测试30分钟，完整测试1小时

            while True:
                # 检查超时
                if time.time() - start_time > max_wait_time:
                    print(f"  ⏰ 测试超时 ({max_wait_time/60:.0f}分钟)")
                    break

                # 获取状态
                status = scheduler.get_status()
                queue_status = status['scheduler']

                # 显示状态
                print(f"  📊 队列状态: 等待={queue_status['pending_count']}, "
                      f"运行={queue_status['running_count']}, "
                      f"完成={queue_status['completed_count']}, "
                      f"失败={queue_status['failed_count']}")

                # 检查是否完成
                if queue_status['pending_count'] == 0 and queue_status['running_count'] == 0:
                    print(f"  ✅ 所有任务已完成")
                    break

                # 等待
                time.sleep(30)  # 每30秒检查一次

            # 生成报告
            print(f"\n{Colors.MAGENTA}[REPORT]{Colors.END} 生成训练报告")
            report = scheduler.generate_report()
            self.test_results['scheduler_stats'] = report['scheduler_report']['statistics']

            # 停止调度器
            scheduler.stop()

            # 检查训练结果
            self._check_training_results()

            return True

        except Exception as e:
            self.test_results['error_message'] = str(e)
            print(f"  ❌ 训练失败: {e}")
            return False

    def _on_task_start(self, task):
        """任务开始回调"""
        print(f"    🏃 任务开始: {task.name}")

    def _on_task_complete(self, task):
        """任务完成回调"""
        print(f"    ✅ 任务完成: {task.name}")
        self.test_results['training_results'][task.name] = {
            'status': 'completed',
            'start_time': task.start_time.isoformat() if task.start_time else None,
            'end_time': task.end_time.isoformat() if task.end_time else None
        }

    def _on_task_error(self, task, error):
        """任务错误回调"""
        print(f"    ❌ 任务失败: {task.name} - {error}")
        self.test_results['training_results'][task.name] = {
            'status': 'failed',
            'error': str(error),
            'start_time': task.start_time.isoformat() if task.start_time else None
        }

    def _check_training_results(self):
        """检查训练结果"""
        print(f"\n{Colors.YELLOW}[CHECK]{Colors.END} 检查训练结果")

        # 检查输出目录
        output_dir = os.path.join(project_root, "output", "training")
        if os.path.exists(output_dir):
            print(f"  📁 输出目录存在: {output_dir}")

            # 统计结果文件
            result_files = []
            for root, dirs, files in os.walk(output_dir):
                for file in files:
                    if file.endswith(('.pth', '.json', '.log')):
                        result_files.append(os.path.join(root, file))

            print(f"  📊 结果文件数量: {len(result_files)}")
            self.test_results['result_files_count'] = len(result_files)
        else:
            print(f"  ⚠️  输出目录不存在")
            self.test_results['result_files_count'] = 0

    def generate_test_report(self):
        """生成测试报告"""
        print(f"\n{Colors.BOLD}[REPORT]{Colors.END} 生成测试报告")

        # 计算测试时间
        end_time = datetime.now()
        duration = end_time - self.test_start_time
        self.test_results['end_time'] = end_time.isoformat()
        self.test_results['duration_minutes'] = duration.total_seconds() / 60

        # 判断测试成功
        stats = self.test_results.get('scheduler_stats', {})
        completed = stats.get('total_completed', 0)
        failed = stats.get('total_failed', 0)

        self.test_results['success'] = (completed > 0 and failed == 0 and
                                       self.test_results['error_message'] is None)

        # 保存报告
        report_file = os.path.join(self.test_dir, "test_report.yaml")
        with open(report_file, 'w', encoding='utf-8') as f:
            yaml.dump(self.test_results, f, default_flow_style=False, allow_unicode=True)

        # 显示摘要
        print(f"\n{Colors.BOLD}📋 测试摘要{Colors.END}")
        print("=" * 60)
        print(f"测试ID: {self.test_results['test_id']}")
        print(f"测试模式: {self.test_results['test_mode']}")
        print(f"测试时长: {self.test_results['duration_minutes']:.1f} 分钟")
        print(f"配置数量: {len(self.test_results['configs_tested'])}")

        if stats:
            print(f"任务统计:")
            print(f"  - 总提交: {stats.get('total_submitted', 0)}")
            print(f"  - 总完成: {stats.get('total_completed', 0)}")
            print(f"  - 总失败: {stats.get('total_failed', 0)}")
            print(f"  - 成功率: {stats.get('success_rate_percent', 0):.1f}%")

        print(f"结果文件: {self.test_results.get('result_files_count', 0)} 个")

        if self.test_results['success']:
            print(f"{Colors.GREEN}✅ 测试成功{Colors.END}")
        else:
            print(f"{Colors.RED}❌ 测试失败{Colors.END}")
            if self.test_results['error_message']:
                print(f"错误信息: {self.test_results['error_message']}")

        print(f"\n📁 测试报告: {report_file}")
        print(f"📁 测试目录: {self.test_dir}")

        return self.test_results['success']

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='SeedVision v1 调度器训练真实测试')
    parser.add_argument('--quick', action='store_true', help='快速测试模式 (2个配置, 5个epoch)')
    parser.add_argument('--full', action='store_true', help='完整测试模式 (4个配置, 10个epoch)')

    args = parser.parse_args()

    # 确定测试模式
    if args.full:
        test_mode = 'full'
    else:
        test_mode = 'quick'  # 默认快速测试

    print(f"{Colors.BOLD}🧪 SeedVision v1 - 调度器训练真实测试{Colors.END}")
    print("=" * 80)

    try:
        # 创建测试实例
        test = SchedulerTrainingTest(test_mode)

        # 执行测试步骤
        test.create_test_config()
        success = test.run_scheduler_training()
        test.generate_test_report()

        # 返回结果
        if success:
            print(f"\n{Colors.GREEN}🎉 调度器训练测试成功完成！{Colors.END}")
            sys.exit(0)
        else:
            print(f"\n{Colors.RED}💥 调度器训练测试失败！{Colors.END}")
            sys.exit(1)

    except KeyboardInterrupt:
        print(f"\n\n{Colors.YELLOW}⚠️  测试被用户中断{Colors.END}")
        sys.exit(1)
    except Exception as e:
        print(f"\n{Colors.RED}❌ 测试异常: {e}{Colors.END}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
