#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的调度器测试 - 启用一些配置进行测试
"""

import os
import sys
import yaml
import tempfile

# 添加项目根路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(project_root)

def create_test_config():
    """创建测试配置文件"""
    # 加载原始配置
    original_config_file = os.path.join(project_root, "config", "training_config.yaml")
    with open(original_config_file, 'r', encoding='utf-8') as f:
        config_data = yaml.safe_load(f)

    # 禁用所有配置
    for config_name in config_data['training_configs']:
        config_data['training_configs'][config_name]['enable'] = False

    # 启用2个小配置用于测试
    test_configs = ['user_56x56_no_norm_high_lr', 'user_112x112_no_norm_low_lr']
    for config_name in test_configs:
        if config_name in config_data['training_configs']:
            config_data['training_configs'][config_name]['enable'] = True
            # 减少训练轮数
            config_data['training_configs'][config_name]['training'] = {'num_epochs': 2}
            print(f"✅ 启用配置: {config_name}")

    # 创建临时配置文件
    temp_config = tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False, encoding='utf-8')
    yaml.dump(config_data, temp_config, default_flow_style=False, allow_unicode=True)
    temp_config.close()

    return temp_config.name

def test_scheduler_with_config():
    """使用配置文件测试调度器"""
    print("🔧 简化调度器测试")
    print("=" * 50)

    # 创建测试配置
    config_file = create_test_config()
    print(f"📁 测试配置文件: {config_file}")

    try:
        # 导入调度器
        from runners.training.main_scheduler import schedule_training

        # 运行调度器
        print("\n🚀 启动调度器...")
        schedule_training(config_file, max_memory=4.0, max_concurrent=1)

    except Exception as e:
        print(f"❌ 调度器测试失败: {e}")
        import traceback
        traceback.print_exc()

    finally:
        # 清理临时文件
        if os.path.exists(config_file):
            os.unlink(config_file)
            print(f"🗑️  清理临时配置文件")

if __name__ == "__main__":
    test_scheduler_with_config()
