# SeedVision v1 - 智能调度系统

## 📋 概述

SeedVision v1 智能调度系统提供了完整的训练任务管理解决方案，包括资源预估、任务调度和进程管理功能。

## 🎯 核心功能

### 1. 资源预估 (ResourceEstimator)
- **GPU显存预估**：基于模型参数、输入尺寸、批次大小预估显存占用
- **训练时间预估**：根据数据量、模型复杂度预估训练时间
- **系统资源监控**：实时监控CPU、内存、GPU使用情况
- **可行性检查**：判断任务是否可以在当前资源下运行

### 2. 任务调度 (TaskScheduler)
- **智能排队**：基于优先级和资源需求智能排队
- **并发控制**：支持多任务并行执行，可配置最大并发数
- **动态调度**：根据资源可用性动态调整任务执行
- **状态管理**：完整的任务状态跟踪和历史记录

### 3. 进程管理 (ProcessManager)
- **进程启动**：安全启动和管理训练进程
- **资源监控**：实时监控进程CPU、内存使用
- **异常处理**：自动处理进程异常和重启
- **日志管理**：完整的进程输出日志管理

## 🚀 快速开始

### 基础使用

```python
from scheduler import SchedulerManager, TrainingTask, TaskPriority

# 创建调度管理器
scheduler = SchedulerManager(
    max_gpu_memory=8.0,      # 最大GPU显存限制
    max_concurrent_tasks=2,   # 最大并发任务数
    log_dir="logs"           # 日志目录
)

# 启动调度器
scheduler.start()

# 创建训练任务
task = TrainingTask(
    task_id='my_task_001',
    name='我的训练任务',
    config=my_config,
    priority=TaskPriority.HIGH
)

# 提交任务
task_id = scheduler.submit_task(task)

# 监控状态
status = scheduler.get_status()
print(f"等待任务: {status['scheduler']['pending_count']}")
print(f"运行任务: {status['scheduler']['running_count']}")

# 停止调度器
scheduler.stop()
```

### 资源预估

```python
from scheduler import ResourceEstimator

estimator = ResourceEstimator()

# 预估资源需求
report = estimator.generate_resource_report(config)
print(f"预估显存: {report['memory_estimate']['total_gb']:.2f} GB")
print(f"预估时间: {report['time_estimate']['total_hours']:.1f} 小时")

# 检查是否可运行
can_run, reason = estimator.can_run_task(config, max_gpu_memory=8.0)
print(f"可运行: {can_run}, 原因: {reason}")
```

## 📊 资源预估详解

### 显存预估公式

```
总显存 = 基础显存 + 参数显存 + 激活值显存 + 梯度显存 + 优化器显存

其中：
- 基础显存: 500 MB (PyTorch基础占用)
- 参数显存: 模型参数数量 × 4 bytes (FP32)
- 激活值显存: 输入尺寸相关的激活值占用
- 梯度显存: 约等于参数显存
- 优化器显存: Adam优化器约为参数显存的2倍
```

### 时间预估公式

```
训练时间 = (样本数量 / 1000) × 时间基准 × epoch数

时间基准 (秒/1000样本):
- 224x224: 120秒
- 112x112: 60秒  
- 56x56: 30秒
```

## 🔧 配置说明

### 任务优先级

```python
class TaskPriority(Enum):
    LOW = 1      # 低优先级
    NORMAL = 2   # 普通优先级
    HIGH = 3     # 高优先级
    URGENT = 4   # 紧急优先级
```

### 任务状态

```python
class TaskStatus(Enum):
    PENDING = "pending"      # 等待中
    RUNNING = "running"      # 运行中
    COMPLETED = "completed"  # 已完成
    FAILED = "failed"        # 失败
    CANCELLED = "cancelled"  # 已取消
    PAUSED = "paused"        # 已暂停
```

### 进程状态

```python
class ProcessStatus(Enum):
    STARTING = "starting"    # 启动中
    RUNNING = "running"      # 运行中
    STOPPING = "stopping"   # 停止中
    STOPPED = "stopped"      # 已停止
    FAILED = "failed"        # 失败
    KILLED = "killed"        # 被终止
```

## 📈 使用场景

### 1. 单任务训练
```python
# 适合：单个大型训练任务
scheduler = SchedulerManager(max_concurrent_tasks=1)
```

### 2. 多任务并行
```python
# 适合：多个小型任务并行训练
scheduler = SchedulerManager(max_concurrent_tasks=3)
```

### 3. 资源受限环境
```python
# 适合：GPU显存有限的环境
scheduler = SchedulerManager(max_gpu_memory=4.0)
```

### 4. 批量实验
```python
# 适合：大量超参数实验
for config in experiment_configs:
    task = TrainingTask(
        task_id=f'exp_{i}',
        name=f'实验_{i}',
        config=config,
        priority=TaskPriority.NORMAL
    )
    scheduler.submit_task(task)
```

## 🛠️ 高级功能

### 任务回调

```python
def on_task_start(task):
    print(f"任务 {task.name} 开始执行")

def on_task_complete(task):
    print(f"任务 {task.name} 执行完成")

def on_task_error(task, error):
    print(f"任务 {task.name} 执行失败: {error}")

task = TrainingTask(
    task_id='callback_task',
    name='回调示例',
    config=config,
    on_start=on_task_start,
    on_complete=on_task_complete,
    on_error=on_task_error
)
```

### 动态资源监控

```python
# 获取实时系统资源
resources = estimator.get_system_resources()

# GPU信息
for gpu_id, gpu_info in resources['gpu'].items():
    print(f"GPU {gpu_id}: {gpu_info['free_gb']:.1f}GB 可用")

# 内存信息
memory = resources['memory']
print(f"系统内存: {memory['available_gb']:.1f}GB 可用")
```

### 任务历史分析

```python
# 获取任务历史
history = scheduler.task_scheduler.get_task_history(limit=20)

for task_info in history:
    print(f"任务: {task_info['name']}")
    print(f"状态: {task_info['status']}")
    print(f"耗时: {task_info['duration_hours']:.1f}小时")
```

## 📋 最佳实践

### 1. 资源规划
- 使用资源预估功能规划任务
- 预留20%的显存余量
- 考虑系统其他进程的资源占用

### 2. 任务设计
- 合理设置任务优先级
- 避免过多并发任务
- 使用回调函数处理任务状态

### 3. 错误处理
- 监控任务失败率
- 及时处理失败任务
- 保存重要的训练日志

### 4. 性能优化
- 根据GPU显存调整批次大小
- 使用混合精度训练节省显存
- 定期清理旧日志文件

## 🔍 故障排除

### 常见问题

1. **显存不足**
   ```
   解决方案：
   - 减小批次大小
   - 降低输入分辨率
   - 使用梯度累积
   ```

2. **任务启动失败**
   ```
   解决方案：
   - 检查配置文件格式
   - 确认工作目录存在
   - 查看错误日志
   ```

3. **进程异常终止**
   ```
   解决方案：
   - 检查系统资源
   - 查看进程日志
   - 验证数据路径
   ```

### 调试技巧

```python
# 启用详细日志
import logging
logging.basicConfig(level=logging.DEBUG)

# 生成详细报告
report = scheduler.generate_report()
print(json.dumps(report, indent=2))

# 检查系统资源
resources = estimator.get_system_resources()
print(f"可用显存: {resources['gpu']['gpu_0']['free_gb']:.1f}GB")
```

## 📚 API 参考

详细的API文档请参考各模块的docstring：

- `ResourceEstimator`: 资源预估相关方法
- `TaskScheduler`: 任务调度相关方法  
- `ProcessManager`: 进程管理相关方法
- `SchedulerManager`: 统一管理接口

## 🎯 未来计划

- [ ] 支持分布式训练调度
- [ ] 添加GPU集群管理
- [ ] 实现自动超参数优化
- [ ] 集成模型性能预测
- [ ] 支持云端资源调度

这个智能调度系统为SeedVision v1提供了完整的训练任务管理能力，让您可以高效地管理多个训练任务，充分利用系统资源。
