from ultralytics import YOLO # 引入YOLO
from .FasterNet import FasterNet as FasterNet_base # 引入基础FasterNet
from .FasterNet import model as FasterNet_model # 引入处理后的FasterNet
from torch import nn # 引入nn模块
from utils.logger import logger # 引入日志记录器
from utils.path_manage import WeightLoader # 引入权重加载器
# 引入Literal
from typing import Literal, TypedDict # 引入Literal，用于固定参数可选项

class TrainOptions(TypedDict):
    yolo: bool
    FasterNet: bool

ModeType = Literal['train_all', 'eval']
class Mixed_YOLO_FasterNet(nn.Module): # 定义MixedModel类，继承自nn.Module
    # 模型类里只设计结构，不设计训练和实现
    def __init__(self, num_classes=2, device='cpu', **kwargs): # 初始化函数
        '''
        初始化函数
        :param num_classes: 分类数量，默认为2
        :param device: 设备，默认为'cpu'
        :param mode: 模式，默认为'train_all'，可选值为'train_all', 'train_yolo', 'train_FasterNet', 'eval'
        :param kwargs: {
            'yolo_path': 'yolov8n.pt', # YOLO模型路径，默认为'yolov8n.pt'，训练和评估时使用
            'FasterNet_path': 'FasterNet.pt', # FasterNet模型路径，默认为'FasterNet.pt'
            'yolo_params': {} yolo训练参数，单独一个字典，具体参数直接参考yolo文档
            'FasterNet_params': {} FasterNet训练参数，单独一个字典，具体参数之后再填
        '''

        super(Mixed_YOLO_FasterNet, self).__init__() # 调用父类的初始化函数
        self.device = device # 将device参数赋值给self.device
        self.__name__ = 'Mixed_YOLO_FasterNet' # 将模型名称赋值给self.__name__
        self.num_classes = num_classes # 将num_classes参数赋值给self.num_classes
        # self.mode = mode # 将mode参数赋值给self.mode
        # self.num_classes = num_classes # 将num_classes参数赋值给self.num_classes
        # self._check_kw(**kwargs) # 检查**kwargs参数函数
        # self.yolo_path = kwargs.get('yolo_path') # 从kwargs中获取yolo_path参数，默认为'yolov8n.pt'
        # self.yolo_params = kwargs.get('yolo_params') # 从kwargs中获取yolo_params参数，默认为{}
        # self.FasterNet_path = kwargs.get('FasterNet_path') # 从kwargs中获取FasterNet_path参数，默认为'FasterNet.pt'
        # self.FasterNet_params = kwargs.get('FasterNet_params') # 从kwargs中获取FasterNet_params参数，默认为{}

        # if mode == 'eval':
        #     if self.yolo_path is None: # 如果yolo_path参数为None
        #         raise ValueError('评估模式下yolo_path参数不能为空') # 抛出异常
        #     if self.FasterNet_path is None: # 如果FasterNet_path参数为None
        #         raise ValueError('评估模式下FasterNet_path参数不能为空') # 抛出异常
        # else:
        #     if mode['yolo']: # 如果mode参数为'train_yolo'
        #         if self.yolo_path is None: # 如果yolo_path参数为None
        #             logger.info(f'yolo_path: {self.yolo_path}，初始化新的yolo11s') # 打印日志
        #             self.yolo_path = 'yolo11s.pt'
        #         else: # 如果yolo_path参数不为None
        #             logger.info(f'yolo_path: {self.yolo_path}，将尝试从上次中断的结果中继续训练') # 打印日志

        #     if mode['FasterNet']: # 如果mode参数为'train_FasterNet'
        #         if self.FasterNet_path is None: # 如果FasterNet_path参数为None
        #             logger.info(f'FasterNet_path: {self.FasterNet_path}，初始化新的FasterNet') # 打印日志
        #             self.FasterNet_path = 'FasterNet.pt' # 将FasterNet_path参数赋值为'FasterNet.pt'
        #         else: # 如果FasterNet_path参数不为None
        #             logger.info(f'FasterNet_path: {self.FasterNet_path}，将尝试从上次中断的结果中继续训练') # 打印日志
    def load_weight(self, weight_path=Literal[list,str]):
        '''
        加载权重函数
        :param weight_path: 权重路径，默认为None，训练和评估时使用
        :return: None
        '''

    def _forward_yolo(self, x): # 定义_forward_yolo函数，用于前向传播YOLO模型
        '''
        定义yolo阶段的前向传播
        '''

    def _forward_FasterNet(self, x): # 定义_forward_FasterNet函数，用于前向传播FasterNet模型
        '''
        定义FasterNet阶段的前向传播
        '''