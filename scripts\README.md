# 📜 SeedVision v1 - 系统脚本目录

本目录包含了 SeedVision v1 系统的各种管理和维护脚本，按功能分类组织。

## 📁 目录结构

```
scripts/
├── deployment/          # 🚀 部署相关脚本
├── maintenance/         # 🔧 维护脚本
├── utilities/           # 🛠️ 工具脚本
└── README.md           # 📖 本文档
```

## 🚀 部署脚本 (deployment/)

### deployment_check.py
**功能**: 部署环境检查脚本
**用途**: 检查系统环境、依赖包、项目结构是否满足部署要求
**使用方法**:
```bash
python scripts/deployment/deployment_check.py
```
**检查项目**:
- Python版本 (>= 3.8)
- 必需依赖包 (torch, numpy, etc.)
- 项目文件结构完整性
- 基础模块导入测试
- 主入口功能测试

### verify_independence.py
**功能**: 项目独立性验证脚本
**用途**: 确保项目完全独立，不依赖外部硬编码路径
**使用方法**:
```bash
python scripts/deployment/verify_independence.py
```
**验证项目**:
- 检查硬编码路径
- 验证相对导入
- 测试核心模块导入
- 确认文件完整性

## 🔧 维护脚本 (maintenance/)

### fix_imports.py
**功能**: 导入路径修复脚本
**用途**: 自动修复项目中的硬编码导入路径，转换为相对路径
**使用方法**:
```bash
python scripts/maintenance/fix_imports.py
```
**修复内容**:
- 硬编码的项目路径
- 错误的相对路径计算
- 导入语句标准化

### test_config_mongo_tool.py
**功能**: 配置MongoDB工具测试脚本
**用途**: 测试配置管理工具的各项功能
**使用方法**:
```bash
python scripts/maintenance/test_config_mongo_tool.py
```
**测试项目**:
- MongoDB连接测试
- YAML配置加载测试
- 配置导入功能测试
- 配置列表功能测试
- 配置对比功能测试

## 🛠️ 工具脚本 (utilities/)

### organize_project.py
**功能**: 项目整理脚本
**用途**: 清理、整理和统计项目文件
**使用方法**:
```bash
# 清理缓存文件
python scripts/utilities/organize_project.py --clean

# 整理日志文件
python scripts/utilities/organize_project.py --logs

# 生成项目报告
python scripts/utilities/organize_project.py --report

# 执行所有操作
python scripts/utilities/organize_project.py --all
```
**功能特性**:
- 清理 `__pycache__`、`.pyc` 等缓存文件
- 按日期归档日志文件
- 检查项目结构完整性
- 生成详细的项目统计报告

## 🎯 使用场景

### 🚀 新环境部署
1. **环境检查**: `python scripts/deployment/deployment_check.py`
2. **独立性验证**: `python scripts/deployment/verify_independence.py`
3. **项目整理**: `python scripts/utilities/organize_project.py --all`

### 🔧 日常维护
1. **清理缓存**: `python scripts/utilities/organize_project.py --clean`
2. **整理日志**: `python scripts/utilities/organize_project.py --logs`
3. **测试配置工具**: `python scripts/maintenance/test_config_mongo_tool.py`

### 🐛 问题排查
1. **修复导入**: `python scripts/maintenance/fix_imports.py`
2. **验证独立性**: `python scripts/deployment/verify_independence.py`
3. **检查环境**: `python scripts/deployment/deployment_check.py`

## 📋 脚本依赖关系

```mermaid
graph TD
    A[deployment_check.py] --> B[verify_independence.py]
    B --> C[fix_imports.py]
    C --> D[test_config_mongo_tool.py]
    D --> E[organize_project.py]
    
    F[新环境部署] --> A
    G[日常维护] --> E
    H[问题排查] --> C
```

## ⚠️ 注意事项

1. **执行路径**: 所有脚本都应该从项目根目录执行
2. **权限要求**: 某些脚本可能需要文件写入权限
3. **备份建议**: 在执行清理操作前建议备份重要文件
4. **依赖检查**: 确保相关依赖包已正确安装

## 🔗 相关文档

- [项目主文档](../README.md)
- [配置指南](../config/CONFIG_GUIDE.md)
- [测试指南](../tests/README.md)
- [调度器文档](../scheduler/README.md)

---

**SeedVision v1 - 让深度学习训练更智能、更高效！**
