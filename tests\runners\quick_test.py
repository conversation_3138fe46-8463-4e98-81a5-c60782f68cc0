#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SeedVision v1 - 快速测试脚本

快速验证系统核心功能是否正常，适合移植后的第一次验证。

功能：
1. 基础导入测试
2. 配置系统测试
3. 调度器功能测试
4. 模型基础测试
5. 生成简要报告

使用方法：
python quick_test.py
"""

import sys
import os
import time
from datetime import datetime

# 添加路径
# 添加项目根路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(project_root)
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
# 添加SeedVision_v1根目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))  # 向上两级到SeedVision_v1
sys.path.insert(0, project_root)

# 颜色定义
class Colors:
    RED = '\033[91m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    MAGENTA = '\033[95m'
    CYAN = '\033[96m'
    WHITE = '\033[97m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'
    END = '\033[0m'

def test_basic_imports():
    """测试基础导入"""
    print("测试基础导入...")

    tests = []

    # 测试配置模块
    try:
        from config.config_loader import ConfigLoader
        tests.append(("ConfigLoader", True, ""))
    except Exception as e:
        tests.append(("ConfigLoader", False, str(e)))

    # 测试模型模块
    try:
        from models.FasterNet import model as FasterNet_model
        tests.append(("FasterNet", True, ""))
    except Exception as e:
        tests.append(("FasterNet", False, str(e)))

    # 测试调度器
    try:
        from scheduler import ResourceEstimator, SchedulerManager
        tests.append(("Scheduler", True, ""))
    except Exception as e:
        tests.append(("Scheduler", False, str(e)))

    # 测试训练工具
    try:
        from tools.training.train import train_model
        from tools.training.validate import calculate_r2
        tests.append(("Training Tools", True, ""))
    except Exception as e:
        tests.append(("Training Tools", False, str(e)))

    # 测试数据工具
    try:
        from tools.data.load_data import load_data
        tests.append(("Data Tools", True, ""))
    except Exception as e:
        tests.append(("Data Tools", False, str(e)))

    return tests

def test_configuration():
    """测试配置系统"""
    print("测试配置系统...")

    try:
        from config.config_loader import ConfigLoader

        # 加载配置
        config_loader = ConfigLoader()
        configs = config_loader.get_enabled_training_configs()

        if configs:
            config = configs[0]
            required_keys = ['name', 'model', 'resources']
            missing = [k for k in required_keys if k not in config]

            if not missing:
                return ("Configuration", True, f"配置正常，找到 {len(configs)} 个配置")
            else:
                return ("Configuration", False, f"配置缺少字段: {missing}")
        else:
            return ("Configuration", False, "未找到启用的配置")

    except Exception as e:
        return ("Configuration", False, str(e))

def test_scheduler():
    """测试调度器"""
    print("测试调度器...")

    try:
        from scheduler import ResourceEstimator, SchedulerManager, TrainingTask, TaskPriority

        # 测试资源预估
        estimator = ResourceEstimator()
        resources = estimator.get_system_resources()

        if 'cpu' not in resources or 'memory' not in resources:
            return ("Scheduler", False, "系统资源获取失败")

        # 测试资源预估
        test_config = {
            'name': 'test',
            'model': {'embed_dim': 64, 'depths': [2, 2, 6, 2]},
            'resources': {'batch_size': 20},
            'transform_config': '224x224_norm',
            'training': {'num_epochs': 10},
            'dataset_config': {'strategy_parameters': {'total_samples': 200}}
        }

        report = estimator.generate_resource_report(test_config)

        if 'memory_estimate' in report and 'can_run' in report:
            memory_gb = report['memory_estimate']['total_gb']
            can_run = report['can_run']
            return ("Scheduler", True, f"资源预估正常: {memory_gb:.2f}GB, 可运行: {can_run}")
        else:
            return ("Scheduler", False, "资源预估功能异常")

    except Exception as e:
        return ("Scheduler", False, str(e))

def test_model():
    """测试模型"""
    print("测试模型...")

    try:
        import torch
        from models.FasterNet import model as FasterNet_model

        # 创建小模型
        model_config = {
            'embed_dim': 32,
            'depths': [1, 1, 2, 1],
            'mlp_ratio': 2.0,
            'n_div': 2,
            'drop_path_rate': 0.1,
            'patch_size': 4,
            'patch_stride': 4,
            'layer_scale_init_value': 0
        }

        model = FasterNet_model(**model_config)

        # 测试前向传播
        test_input = torch.randn(1, 3, 224, 224)
        with torch.no_grad():
            output = model(test_input)

        # 检查输出形状
        if hasattr(output, 'shape'):
            expected_shape = (1, 2)
            if output.shape == expected_shape:
                return ("Model", True, f"模型正常，输出形状: {output.shape}")
            else:
                return ("Model", False, f"模型输出形状错误: 期望{expected_shape}, 实际{output.shape}")
        else:
            return ("Model", False, f"模型输出类型错误: {type(output)}")

    except Exception as e:
        return ("Model", False, str(e))

def test_file_structure():
    """测试文件结构"""
    print("测试文件结构...")

    critical_paths = [
        'config/config_loader.py',
        'config/training_config.yaml',
        'models/FasterNet.py',
        'scheduler/resource_estimator.py',
        'tools/training/train.py',
        'tools/data/load_data.py',
        'main.py',
        'main_scheduler.py'
    ]

    missing = []
    for path in critical_paths:
        if not os.path.exists(path):
            missing.append(path)

    if not missing:
        return ("File Structure", True, f"所有关键文件存在 ({len(critical_paths)}个)")
    else:
        return ("File Structure", False, f"缺少文件: {missing[:3]}{'...' if len(missing) > 3 else ''}")

def run_quick_test():
    """运行快速测试"""
    print("SeedVision v1 - 快速测试")
    print("=" * 60)
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()

    start_time = time.time()
    all_results = []

    # 运行各项测试
    all_results.append(test_file_structure())

    import_results = test_basic_imports()
    all_results.extend(import_results)

    all_results.append(test_configuration())
    all_results.append(test_scheduler())
    all_results.append(test_model())

    # 统计结果
    total_tests = len(all_results)
    passed_tests = sum(1 for _, success, _ in all_results if success)
    failed_tests = total_tests - passed_tests

    duration = time.time() - start_time

    # 显示结果
    print(f"\n{Colors.BOLD}[RESULTS]{Colors.END} 测试结果:")
    print("-" * 60)

    for test_name, success, message in all_results:
        status = f"{Colors.GREEN}[PASS]{Colors.END}" if success else f"{Colors.RED}[FAIL]{Colors.END}"
        print(f"{status} {test_name}")
        if message:
            print(f"    {message}")

    print("\n" + "=" * 60)
    print(f"{Colors.CYAN}[STATS]{Colors.END} 测试统计:")
    print(f"   总测试数: {total_tests}")
    print(f"   通过: {passed_tests}")
    print(f"   失败: {failed_tests}")
    print(f"   成功率: {(passed_tests/total_tests*100):.1f}%")
    print(f"   耗时: {duration:.1f}秒")

    # 结论
    print(f"\n{Colors.BOLD}[CONCLUSION]{Colors.END} 测试结论:")
    if failed_tests == 0:
        print(f"{Colors.GREEN}[SUCCESS]{Colors.END} 所有测试通过！系统基础功能正常。")
        print("可以继续进行完整测试或开始使用系统。")
        print(f"\n{Colors.BLUE}[INFO]{Colors.END} 下一步建议:")
        print("   1. 运行完整测试: python system_test.py")
        print("   2. 测试调度器: python main_scheduler.py --mode estimate")
        print("   3. 开始训练: python main.py --sequential")
    else:
        print(f"{Colors.RED}[FAILED]{Colors.END} 有 {failed_tests} 个测试失败。")
        print("请先修复失败的测试项，然后重新运行测试。")
        print(f"\n{Colors.YELLOW}[WARNING]{Colors.END} 故障排除建议:")
        print("   1. 检查Python路径设置")
        print("   2. 确认所有文件已正确移动")
        print("   3. 检查依赖包是否安装")

    return failed_tests == 0

def main():
    """主函数"""
    try:
        success = run_quick_test()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
